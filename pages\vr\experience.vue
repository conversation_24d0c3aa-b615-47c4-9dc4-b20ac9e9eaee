<template>
	<view class="vr-container">
		<!-- 自定义导航栏 -->
		<view class="vr-navbar">
			<view class="navbar-content">
				<view class="back-btn" @click="goBack">
					<text class="back-icon">←</text>
				</view>
				<text class="navbar-title">{{ scenicName }} VR体验</text>
				<view class="placeholder"></view>
			</view>
		</view>
		
		<!-- VR体验区域 -->
		<view class="vr-content">
			<!-- 加载提示 -->
			<view class="loading-overlay" v-if="isLoading">
				<view class="loading-box">
					<text class="loading-icon">🥽</text>
					<text class="loading-text">正在加载VR体验...</text>
					<text class="loading-desc">请稍候，即将进入{{ scenicName }}的虚拟世界</text>
				</view>
			</view>
			
			<!-- VR Web-view -->
			<web-view 
				v-if="vrUrl" 
				:src="vrUrl" 
				@message="onMessage"
				@load="onLoad"
				@error="onError"
			></web-view>
			
			<!-- 错误提示 -->
			<view class="error-box" v-if="hasError">
				<text class="error-icon">😔</text>
				<text class="error-title">VR体验加载失败</text>
				<text class="error-desc">请检查网络连接后重试</text>
				<view class="retry-btn" @click="retryLoad">
					<text class="btn-text">重新加载</text>
				</view>
			</view>
		</view>
		
		<!-- VR体验说明 -->
		<view class="vr-tips" v-if="!isLoading && !hasError">
			<view class="tips-content">
				<text class="tips-icon">💡</text>
				<text class="tips-text">拖拽屏幕可360°观看，双指缩放调整视角</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			scenicId: null,
			scenicName: '',
			vrUrl: '',
			isLoading: true,
			hasError: false,
			
			// VR链接映射
			vrLinks: {
				1: 'https://www.720yun.com/t/9dcjvzmkea7?scene_id=20012473',  // 南岳衡山
				2: 'https://www.720yun.com/t/38cjt7umvO2?scene_id=14943450',  // 石鼓书院
				3: 'https://www.720yun.com/t/899jv7yOuy3?scene_id=21330843',  // 回雁峰
				4: 'https://www.720yun.com/t/3a62cmz8cbn?scene_id=543271',    // 南华大学
				5: 'https://www.720yun.com/t/a4vki9ryg87?scene_id=34031063',  // 东洲岛
				6: 'https://www.720yun.com/t/25vkib1h729?scene_id=37482643'   // 岣嵝峰
			},
			
			// 景点名称映射
			scenicNames: {
				1: '南岳衡山',
				2: '石鼓书院', 
				3: '回雁峰',
				4: '南华大学',
				5: '东洲岛',
				6: '岣嵝峰'
			}
		}
	},
	
	onLoad(options) {
		console.log('VR页面参数:', options)
		
		// 获取景点ID
		this.scenicId = parseInt(options.scenicId) || 1
		this.scenicName = this.scenicNames[this.scenicId] || '未知景点'
		this.vrUrl = this.vrLinks[this.scenicId]
		
		if (!this.vrUrl) {
			this.hasError = true
			this.isLoading = false
			return
		}
		
		// 设置页面标题
		uni.setNavigationBarTitle({
			title: `${this.scenicName} VR体验`
		})
		
		console.log('准备加载VR:', this.vrUrl)
	},
	
	methods: {
		// Web-view加载完成
		onLoad() {
			console.log('VR页面加载完成')
			this.isLoading = false
			this.hasError = false
		},
		
		// Web-view加载错误
		onError(e) {
			console.error('VR页面加载错误:', e)
			this.isLoading = false
			this.hasError = true
			
			uni.showToast({
				title: 'VR体验加载失败',
				icon: 'none'
			})
		},
		
		// 接收Web-view消息
		onMessage(e) {
			console.log('收到VR页面消息:', e)
		},
		
		// 重新加载
		retryLoad() {
			this.isLoading = true
			this.hasError = false
			
			// 重新设置URL触发重新加载
			const currentUrl = this.vrUrl
			this.vrUrl = ''
			this.$nextTick(() => {
				this.vrUrl = currentUrl
			})
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack({
				fail: () => {
					// 如果没有上一页，跳转到首页
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}
			})
		}
	}
}
</script>

<style lang="scss">
.vr-container {
	width: 100vw;
	height: 100vh;
	background: #000;
	position: relative;
	overflow: hidden;
}

.vr-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(0, 0, 0, 0.8);
	backdrop-filter: blur(10rpx);
	
	.navbar-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		padding-top: calc(20rpx + var(--status-bar-height, 0));
		
		.back-btn {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50%;
			
			.back-icon {
				font-size: 32rpx;
				color: #FFF;
				font-weight: bold;
			}
		}
		
		.navbar-title {
			font-size: 32rpx;
			color: #FFD700;
			font-weight: bold;
			text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.8);
		}
		
		.placeholder {
			width: 60rpx;
		}
	}
}

.vr-content {
	width: 100%;
	height: 100%;
	position: relative;
}

.loading-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;
	
	.loading-box {
		text-align: center;
		
		.loading-icon {
			font-size: 120rpx;
			display: block;
			margin-bottom: 40rpx;
			animation: pulse 2s infinite;
		}
		
		.loading-text {
			font-size: 36rpx;
			color: #FFD700;
			font-weight: bold;
			display: block;
			margin-bottom: 20rpx;
		}
		
		.loading-desc {
			font-size: 28rpx;
			color: #CCC;
			display: block;
		}
	}
}

.error-box {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
	padding: 60rpx;
	
	.error-icon {
		font-size: 120rpx;
		display: block;
		margin-bottom: 40rpx;
	}
	
	.error-title {
		font-size: 36rpx;
		color: #FF6B6B;
		font-weight: bold;
		display: block;
		margin-bottom: 20rpx;
	}
	
	.error-desc {
		font-size: 28rpx;
		color: #CCC;
		display: block;
		margin-bottom: 40rpx;
	}
	
	.retry-btn {
		background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
		padding: 20rpx 40rpx;
		border-radius: 40rpx;
		display: inline-block;
		
		.btn-text {
			color: #FFF;
			font-size: 28rpx;
			font-weight: bold;
		}
	}
}

.vr-tips {
	position: fixed;
	bottom: 30rpx;
	left: 30rpx;
	right: 30rpx;
	z-index: 998;
	
	.tips-content {
		background: rgba(0, 0, 0, 0.7);
		padding: 20rpx 30rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		backdrop-filter: blur(10rpx);
		
		.tips-icon {
			font-size: 28rpx;
			margin-right: 15rpx;
		}
		
		.tips-text {
			font-size: 24rpx;
			color: #FFF;
		}
	}
}

@keyframes pulse {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.1); }
}
</style>
