/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.home-view {
  width: 100vw;
  height: 100vh;
}
.swiper {
  width: 100vw;
  height: 100vh;
}
.swiper-item {
  position: relative;
  width: 100%;
  height: 100%;
}
.swiper-item .item-box {
  position: relative;
  width: 100%;
  height: 100%;
}
.swiper-item .bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}
.swiper-item .bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}
.swiper-item .swiper-windows {
  height: 546rpx;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  z-index: 2;
}
.swiper-item .swiper-windows .windows-image {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 520rpx;
  height: 520rpx;
  opacity: 0;
  transition: all 3s ease-in-out;
  border-radius: 50%;
  object-fit: cover;
}
.swiper-item .swiper-windows .windows-bg,
.swiper-item .swiper-windows .windows-mask {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 546rpx;
  height: 546rpx;
  z-index: 99;
  border-radius: 50%;
}
.swiper-item .swiper-windows .windows-mask {
  background: rgba(0, 0, 0, 0.1);
  border: 8rpx solid rgba(139, 69, 19, 0.8);
}
.swiper-item .swiper-windows .windows-bg {
  background: rgba(139, 69, 19, 0.2);
  border: 4rpx solid rgba(139, 69, 19, 0.6);
}
.title-image {
  position: absolute;
  bottom: 25%;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  z-index: 3;
}
.title-image .title-text {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #FFD700;
  text-shadow: 2rpx 2rpx 6rpx rgba(0, 0, 0, 0.8);
  margin-bottom: 10rpx;
}
.title-image .subtitle-text {
  display: block;
  font-size: 28rpx;
  color: #FFF8DC;
  text-shadow: 1rpx 1rpx 4rpx rgba(0, 0, 0, 0.6);
}
.flex-box-space-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.recommend-swiper {
  position: absolute;
  bottom: 15%;
  left: 50%;
  transform: translateX(-50%);
  width: 702rpx;
  height: 120rpx;
  /* 增加空间感 */
  z-index: 3;
  /* vue3.0 transition not work in swiper-item, we use css animation */
}
.recommend-swiper .swiper-item {
  animation: fadeIn 1s ease-in-out;
}
@keyframes fadeIn {
from {
    opacity: 0.5;
}
to {
    opacity: 1;
}
}
.recommend-swiper .recommend-item {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  /* 暗色背景 */
  border-radius: 16rpx;
  /* 更圆润的边角 */
  padding: 0 30rpx;
  box-sizing: border-box;
  border: 1rpx solid rgba(255, 215, 0, 0.3);
  /* 金色辉光边框 */
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  /* 毛玻璃效果 */
}
.recommend-swiper .recommend-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}
.recommend-swiper .recommend-text-content {
  display: flex;
  flex-direction: column;
}
.recommend-swiper .recommend-title {
  font-size: 28rpx;
  color: #FFD700;
  /* 金色标题 */
  font-weight: bold;
}
.recommend-swiper .recommend-subtitle {
  font-size: 22rpx;
  color: #FFF8DC;
  /* 米白副标题 */
}

/* .tips-box {
		position: absolute;
		bottom: 15%;
		left: 50%;
		transform: translateX(-50%);
		width: 702rpx;
		height: 82rpx;
		background: rgba(255, 255, 255, 0.9);
		border-radius: 12rpx;
		padding: 0 30rpx;
		box-sizing: border-box;
		font-size: 26rpx;
		color: #333;
		font-weight: bold;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
		z-index: 3;

		.tips-icon {
			font-size: 32rpx;
			margin-right: 10rpx;
		}

		.tips-text {
			flex: 1;
			margin-left: 20rpx;
		}
	} */
.button-group {
  position: absolute;
  bottom: 5%;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  /* 确保按钮垂直居中对齐 */
  gap: 20rpx;
  z-index: 3;
}
.explore-btn {
  width: 280rpx;
  /* 调整宽度以适应双按钮布局 */
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 12rpx rgba(139, 69, 19, 0.3);
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
}
.explore-btn .btn-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: bold;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.3);
}
.footprints-btn {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.2);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  border: 1rpx solid rgba(255, 215, 0, 0.5);
}
.footprints-btn .btn-icon {
  font-size: 40rpx;
  color: #8B4513;
  font-weight: bold;
}
.weather-badge {
  position: absolute;
  top: 60rpx;
  /* 根据你的状态栏高度调整 */
  left: 30rpx;
  z-index: 10;
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 30rpx;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 215, 0, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.weather-badge .weather-icon {
  font-size: 40rpx;
  margin-right: 15rpx;
}
.weather-badge .weather-info {
  display: flex;
  flex-direction: column;
  color: #fff;
}
.weather-badge .temperature {
  font-size: 32rpx;
  font-weight: bold;
}
.weather-badge .city-name {
  font-size: 22rpx;
  color: #eee;
  opacity: 0.9;
}