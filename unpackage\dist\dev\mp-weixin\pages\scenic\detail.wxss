/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.back-button {
  position: absolute;
  top: calc(var(--status-bar-height) + 20rpx);
  left: 30rpx;
  z-index: 1000;
  width: 70rpx;
  height: 70rpx;
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.back-button .back-icon {
  font-size: 50rpx;
  color: #fff;
}
.detail-container {
  width: 100vw;
  min-height: 100vh;
  background: linear-gradient(180deg, #2d1b1b 0%, #4a3226 50%, #3d2817 100%);
  position: relative;
  overflow: hidden;
}
.immersive-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 100vh;
  z-index: 10;
}
.immersive-header .hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.immersive-header .hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.immersive-header .hero-overlay .hero-gradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60%;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
}
.immersive-header .hero-overlay .hero-content {
  position: absolute;
  bottom: 150rpx;
  left: 60rpx;
  right: 60rpx;
}
.immersive-header .hero-overlay .hero-content .hero-title {
  display: block;
  font-size: 80rpx;
  color: #FFD700;
  font-weight: 900;
  text-shadow: 2rpx 2rpx 8rpx rgba(0, 0, 0, 0.8);
  margin-bottom: 20rpx;
  letter-spacing: 4rpx;
}
.immersive-header .hero-overlay .hero-content .hero-subtitle {
  display: block;
  font-size: 32rpx;
  color: #FFF8DC;
  text-shadow: 1rpx 1rpx 4rpx rgba(0, 0, 0, 0.6);
  letter-spacing: 2rpx;
}
.floating-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(45, 27, 27, 0.9);
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(255, 215, 0, 0.3);
  transform: translateY(-100%);
  transition: transform 0.3s ease;
  z-index: 1000;
}
.floating-navbar.navbar-visible {
  transform: translateY(0);
}
.floating-navbar .navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 20rpx 30rpx;
  padding-top: calc(20rpx + var(--status-bar-height, 0));
}
.floating-navbar .navbar-content .back-btn, .floating-navbar .navbar-content .share-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 50%;
  border: 1rpx solid rgba(255, 215, 0, 0.3);
}
.floating-navbar .navbar-content .back-btn .back-icon, .floating-navbar .navbar-content .back-btn .share-icon, .floating-navbar .navbar-content .share-btn .back-icon, .floating-navbar .navbar-content .share-btn .share-icon {
  font-size: 32rpx;
  color: #FFD700;
  font-weight: bold;
}
.floating-navbar .navbar-content .navbar-title {
  font-size: 32rpx;
  color: #FFD700;
  font-weight: bold;
  max-width: 400rpx;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: rgba(255, 215, 0, 0.2);
  z-index: 1001;
}
.scroll-progress .progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #FFD700, #FFA500);
  transition: width 0.1s ease;
  box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.5);
}
.scroll-content {
  position: relative;
  z-index: 20;
  background: transparent;
}
.content-spacer {
  height: 100vh;
}
.scroll-container {
  background: linear-gradient(180deg, transparent 0%, rgba(45, 27, 27, 0.95) 10%, rgba(61, 40, 23, 0.98) 20%);
  min-height: 100vh;
  padding: 60rpx 0;
}
.scroll-section {
  margin-bottom: 120rpx;
  padding: 0 60rpx;
  opacity: 0;
  transform: translateY(100rpx);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}
.scroll-section.section-active {
  opacity: 1;
  transform: translateY(0);
}
.scroll-section .section-header {
  display: flex;
  align-items: center;
  margin-bottom: 60rpx;
}
.scroll-section .section-header .section-number {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-right: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(255, 215, 0, 0.3);
}
.scroll-section .section-header .section-title {
  font-size: 48rpx;
  color: #FFD700;
  font-weight: bold;
  letter-spacing: 4rpx;
}
.intro-card {
  position: relative;
  background: rgba(74, 50, 38, 0.85);
  border-radius: 20rpx;
  padding: 60rpx;
  border: 2rpx solid rgba(255, 215, 0, 0.3);
  box-shadow: 0 20rpx 40rpx rgba(45, 27, 27, 0.4);
}
.intro-card .card-ornament {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #FFD700;
}
.intro-card .card-ornament.top-left {
  top: 20rpx;
  left: 20rpx;
  border-right: none;
  border-bottom: none;
}
.intro-card .card-ornament.top-right {
  top: 20rpx;
  right: 20rpx;
  border-left: none;
  border-bottom: none;
}
.intro-card .card-ornament.bottom-left {
  bottom: 20rpx;
  left: 20rpx;
  border-right: none;
  border-top: none;
}
.intro-card .card-ornament.bottom-right {
  bottom: 20rpx;
  right: 20rpx;
  border-left: none;
  border-top: none;
}
.intro-card .intro-content .intro-text {
  font-size: 32rpx;
  line-height: 1.8;
  color: #FFF8DC;
  margin-bottom: 40rpx;
  text-indent: 2em;
}
.intro-card .intro-content .intro-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.intro-card .intro-content .intro-tags .tag {
  padding: 12rpx 24rpx;
  background: rgba(255, 215, 0, 0.1);
  border: 1rpx solid rgba(255, 215, 0, 0.3);
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #FFD700;
}
.timeline-container {
  position: relative;
}
.timeline-container .timeline-line {
  position: absolute;
  left: 40rpx;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: linear-gradient(180deg, #FFD700, #FFA500);
  border-radius: 2rpx;
}
.timeline-container .timeline-item {
  position: relative;
  padding-left: 120rpx;
  margin-bottom: 60rpx;
  opacity: 0;
  transform: translateX(-50rpx);
  transition: all 0.6s ease;
  transition-delay: var(--delay);
}
.timeline-container .timeline-item.timeline-active {
  opacity: 1;
  transform: translateX(0);
}
.timeline-container .timeline-item .timeline-dot {
  position: absolute;
  left: 28rpx;
  top: 10rpx;
  width: 24rpx;
  height: 24rpx;
  background: #FFD700;
  border-radius: 50%;
  box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.5);
}
.timeline-container .timeline-item .timeline-content {
  background: rgba(44, 24, 16, 0.6);
  padding: 30rpx;
  border-radius: 15rpx;
  border-left: 4rpx solid #FFD700;
}
.timeline-container .timeline-item .timeline-content .timeline-period {
  display: block;
  font-size: 28rpx;
  color: #FFD700;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.timeline-container .timeline-item .timeline-content .timeline-desc {
  font-size: 26rpx;
  color: #FFF8DC;
  line-height: 1.6;
}
.culture-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}
.culture-grid .culture-card {
  background: rgba(44, 24, 16, 0.6);
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
  border: 2rpx solid transparent;
  transition: all 0.4s ease;
  transform: scale(0.95);
  opacity: 0;
}
.culture-grid .culture-card.card-active {
  opacity: 1;
  transform: scale(1);
}
.culture-grid .culture-card:active {
  transform: scale(0.98);
  border-color: #FFD700;
  background: rgba(44, 24, 16, 0.8);
}
.culture-grid .culture-card .culture-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}
.culture-grid .culture-card .culture-name {
  display: block;
  font-size: 28rpx;
  color: #FFD700;
  font-weight: bold;
  margin-bottom: 15rpx;
}
.culture-grid .culture-card .culture-desc {
  font-size: 24rpx;
  color: #FFF8DC;
  line-height: 1.5;
}
.compare-container {
  position: relative;
  height: 400rpx;
  border-radius: 20rpx;
  overflow: hidden;
}
.compare-container .compare-slider {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background: #FFD700;
  z-index: 10;
  transition: left 0.1s ease;
}
.compare-container .compare-slider .slider-handle {
  position: absolute;
  top: 50%;
  left: -15rpx;
  width: 34rpx;
  height: 34rpx;
  background: #FFD700;
  border-radius: 50%;
  transform: translateY(-50%);
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.4);
}
.compare-container .compare-slider .slider-label {
  position: absolute;
  top: 20rpx;
  left: -60rpx;
  width: 120rpx;
  text-align: center;
  font-size: 24rpx;
  color: #FFD700;
  background: rgba(26, 26, 26, 0.8);
  padding: 8rpx;
  border-radius: 10rpx;
}
.compare-container .compare-images {
  position: relative;
  width: 100%;
  height: 100%;
}
.compare-container .compare-images .compare-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.compare-container .compare-images .compare-image.modern {
  z-index: 5;
}
.guide-content .guide-item {
  display: flex;
  align-items: center;
  background: rgba(44, 24, 16, 0.6);
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-left: 4rpx solid #FFD700;
}
.guide-content .guide-item .guide-icon {
  font-size: 40rpx;
  margin-right: 30rpx;
}
.guide-content .guide-item .guide-text {
  flex: 1;
}
.guide-content .guide-item .guide-text .guide-label {
  display: block;
  font-size: 24rpx;
  color: #FFD700;
  margin-bottom: 8rpx;
}
.guide-content .guide-item .guide-text .guide-value {
  font-size: 28rpx;
  color: #FFF8DC;
  line-height: 1.5;
}
.bottom-actions {
  margin-top: 40rpx;
}
.bottom-actions .action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
  padding: 0 60rpx;
}
.bottom-actions .action-grid .action-item {
  background: rgba(44, 24, 16, 0.8);
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
  border: 2rpx solid rgba(255, 215, 0, 0.2);
  transition: all 0.3s ease;
}
.bottom-actions .action-grid .action-item.map-back-item {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border: 3rpx solid #8B4513;
  transform: scale(1.05);
}
.bottom-actions .action-grid .action-item.map-back-item .action-icon {
  font-size: 60rpx;
}
.bottom-actions .action-grid .action-item.map-back-item .action-text {
  color: #1a1a1a;
  font-size: 30rpx;
}
.bottom-actions .action-grid .action-item.map-back-item:active {
  transform: scale(1);
  background: linear-gradient(135deg, #FFA500, #FF8C00);
}
.bottom-actions .action-grid .action-item:active {
  transform: scale(0.95);
  background: rgba(44, 24, 16, 0.9);
  border-color: #FFD700;
}
.bottom-actions .action-grid .action-item .action-icon {
  font-size: 50rpx;
  margin-bottom: 15rpx;
}
.bottom-actions .action-grid .action-item .action-text {
  display: block;
  font-size: 26rpx;
  color: #FFF8DC;
  font-weight: bold;
}
.culture-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 60rpx;
}
.culture-modal .modal-content {
  background: #1a1a1a;
  border-radius: 20rpx;
  border: 2rpx solid #FFD700;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}
.culture-modal .modal-content .modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid rgba(255, 215, 0, 0.2);
}
.culture-modal .modal-content .modal-header .modal-title {
  font-size: 32rpx;
  color: #FFD700;
  font-weight: bold;
}
.culture-modal .modal-content .modal-header .modal-close {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #FFD700;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 50%;
}
.culture-modal .modal-content .modal-body {
  padding: 40rpx;
}
.culture-modal .modal-content .modal-body .modal-text {
  font-size: 28rpx;
  color: #FFF8DC;
  line-height: 1.8;
}
.checkin-icon {
  background-color: #f0f0f0;
  color: #8B4513;
  font-weight: bold;
  border: 2rpx solid #8B4513;
}
.checkin-icon.checked-in {
  background-color: #8B4513;
  color: #FFD700;
  transform: scale(1.1) rotate(15deg);
  box-shadow: 0 0 15rpx #FFD700;
}