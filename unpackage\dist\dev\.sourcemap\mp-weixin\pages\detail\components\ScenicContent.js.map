{"version": 3, "file": "ScenicContent.js", "sources": ["pages/detail/components/ScenicContent.vue", "../../../../App/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovVXNlcnMvSkxIdWFuZy9EZXNrdG9wL3l1blZyL3BhZ2VzL2RldGFpbC9jb21wb25lbnRzL1NjZW5pY0NvbnRlbnQudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"content-container\">\r\n\t\t<!-- 沉浸式头部 -->\r\n\t\t<view class=\"immersive-header\" :style=\"{ transform: `translateY(${headerOffset}px)` }\">\r\n\t\t\t<image \r\n\t\t\t\t:src=\"content.viewImage\" \r\n\t\t\t\tclass=\"hero-image\" \r\n\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t:style=\"{ transform: `scale(${1 + scrollY * 0.0005})` }\"\r\n\t\t\t></image>\r\n\t\t\t<view class=\"hero-overlay\">\r\n\t\t\t\t<view class=\"hero-gradient\"></view>\r\n\t\t\t\t<view class=\"hero-content\">\r\n\t\t\t\t\t<text class=\"hero-title\" :style=\"{ transform: `translateY(${titleOffset}px)`, opacity: titleOpacity }\">\r\n\t\t\t\t\t\t{{ content.name }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t\t<text class=\"hero-subtitle\" :style=\"{ transform: `translateY(${subtitleOffset}px)`, opacity: subtitleOpacity }\">\r\n\t\t\t\t\t\t{{ content.category }} · {{ content.era }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 浮动导航栏 -->\r\n\t\t\t<view class=\"floating-navbar\" :class=\"{ 'navbar-visible': showNavbar }\">\r\n\t\t\t\t<view class=\"navbar-content\">\r\n\t\t\t\t\t<view class=\"back-btn\" @click=\"goBack\">\r\n\t\t\t\t\t\t<text class=\"back-icon\">←</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class=\"navbar-title\">{{ content.name }}</text>\r\n\t\t\t\t\t<view class=\"share-btn\" @click=\"shareContent\">\r\n\t\t\t\t\t\t<text class=\"share-icon\">⚡</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 滚动进度指示器 -->\r\n\t\t<view class=\"scroll-progress\">\r\n\t\t\t<view class=\"progress-bar\" :style=\"{ width: scrollProgress + '%' }\"></view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 卷轴内容区域 -->\r\n\t\t<scroll-view \r\n\t\t\tclass=\"scroll-content\" \r\n\t\t\tscroll-y=\"true\" \r\n\t\t\t@scroll=\"onScroll\"\r\n\t\t\t:scroll-top=\"scrollTop\"\r\n\t\t\tscroll-with-animation=\"true\"\r\n\t\t>\r\n\t\t\t<!-- 内容间隔 -->\r\n\t\t\t<view class=\"content-spacer\"></view>\r\n\t\t\t\r\n\t\t\t<!-- 卷轴展开动画区域 -->\r\n\t\t\t<view class=\"scroll-container\">\r\n\t\t\t\t\r\n\t\t\t\t<!-- 第一卷：基本信息 -->\r\n\t\t\t\t<view class=\"scroll-section intro-section\" :class=\"{ 'section-active': activeSection >= 0 }\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"section-number\">壹</view>\r\n\t\t\t\t\t\t<text class=\"section-title\">古韵初识</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"intro-card\">\r\n\t\t\t\t\t\t<view class=\"card-ornament top-left\"></view>\r\n\t\t\t\t\t\t<view class=\"card-ornament top-right\"></view>\r\n\t\t\t\t\t\t<view class=\"card-ornament bottom-left\"></view>\r\n\t\t\t\t\t\t<view class=\"card-ornament bottom-right\"></view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"intro-content\">\r\n\t\t\t\t\t\t\t<text class=\"intro-text\">{{ content.introduction }}</text>\r\n\t\t\t\t\t\t\t<view class=\"intro-tags\">\r\n\t\t\t\t\t\t\t\t<text v-for=\"tag in content.tags\" :key=\"tag\" class=\"tag\">{{ tag }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 第二卷：历史传承 -->\r\n\t\t\t\t<view v-if=\"content.history && content.history.length > 0\" class=\"scroll-section history-section\" :class=\"{ 'section-active': activeSection >= 1 }\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"section-number\">贰</view>\r\n\t\t\t\t\t\t<text class=\"section-title\">历史传承</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"timeline-container\">\r\n\t\t\t\t\t\t<view class=\"timeline-line\"></view>\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tv-for=\"(period, index) in content.history\" \r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\tclass=\"timeline-item\"\r\n\t\t\t\t\t\t\t:class=\"{ 'timeline-active': activeTimelineItem >= index }\"\r\n\t\t\t\t\t\t\t:style=\"{ '--delay': index * 0.2 + 's' }\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<view class=\"timeline-dot\"></view>\r\n\t\t\t\t\t\t\t<view class=\"timeline-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"timeline-period\">{{ period.era }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"timeline-desc\">{{ period.description }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 第三卷：文化内涵 -->\r\n\t\t\t\t<view v-if=\"content.culture && content.culture.length > 0\" class=\"scroll-section culture-section\" :class=\"{ 'section-active': activeSection >= 2 }\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"section-number\">叁</view>\r\n\t\t\t\t\t\t<text class=\"section-title\">文化内涵</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"culture-grid\">\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tv-for=\"(aspect, index) in content.culture\" \r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\tclass=\"culture-card\"\r\n\t\t\t\t\t\t\t:class=\"{ 'card-active': activeCultureCard >= index }\"\r\n\t\t\t\t\t\t\t@click=\"showCultureDetail(aspect)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<view class=\"culture-icon\">{{ aspect.icon }}</view>\r\n\t\t\t\t\t\t\t<text class=\"culture-name\">{{ aspect.name }}</text>\r\n\t\t\t\t\t\t\t<text class=\"culture-desc\">{{ aspect.description }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 第四卷：今昔对比 -->\r\n\t\t\t\t<view class=\"scroll-section compare-section\" :class=\"{ 'section-active': activeSection >= 3 }\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"section-number\">肆</view>\r\n\t\t\t\t\t\t<text class=\"section-title\">古今对话</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"compare-container\">\r\n\t\t\t\t\t\t<view class=\"compare-slider\" :style=\"{ left: compareSliderPosition + '%' }\">\r\n\t\t\t\t\t\t\t<view class=\"slider-handle\"></view>\r\n\t\t\t\t\t\t\t<text class=\"slider-label\">{{ compareSliderPosition < 50 ? '古韵悠长' : '今日风华' }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"compare-images\" @touchmove=\"onCompareSlide\">\r\n\t\t\t\t\t\t\t<image :src=\"content.historicalImage\" class=\"compare-image historical\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t<image \r\n\t\t\t\t\t\t\t\t:src=\"content.viewImage\" \r\n\t\t\t\t\t\t\t\tclass=\"compare-image modern\" \r\n\t\t\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t\t\t:style=\"{ clipPath: `polygon(${compareSliderPosition}% 0%, 100% 0%, 100% 100%, ${compareSliderPosition}% 100%)` }\"\r\n\t\t\t\t\t\t\t></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 第五卷：游览指南 -->\r\n\t\t\t\t<view class=\"scroll-section guide-section\" :class=\"{ 'section-active': activeSection >= 4 }\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"section-number\">伍</view>\r\n\t\t\t\t\t\t<text class=\"section-title\">游览指南</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"guide-content\">\r\n\t\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t\t<view class=\"guide-icon\">🕐</view>\r\n\t\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t\t<text class=\"guide-label\">最佳时节</text>\r\n\t\t\t\t\t\t\t\t<text class=\"guide-value\">{{ content.bestTime }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t\t<view class=\"guide-icon\">🎫</view>\r\n\t\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t\t<text class=\"guide-label\">门票信息</text>\r\n\t\t\t\t\t\t\t\t<text class=\"guide-value\">{{ content.ticketInfo }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t\t<view class=\"guide-icon\">🚌</view>\r\n\t\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t\t<text class=\"guide-label\">交通指南</text>\r\n\t\t\t\t\t\t\t\t<text class=\"guide-value\">{{ content.transportation }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t\t<view class=\"guide-icon\">⭐</view>\r\n\t\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t\t<text class=\"guide-label\">游览建议</text>\r\n\t\t\t\t\t\t\t\t<text class=\"guide-value\">{{ content.tips }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 底部操作区 -->\r\n\t\t\t\t<view class=\"bottom-actions\">\r\n\t\t\t\t\t<view class=\"action-grid\">\r\n\t\t\t\t\t\t<view class=\"action-item map-back-item\" @click=\"showOnMap\">\r\n\t\t\t\t\t\t\t<view class=\"action-icon map-icon\">🗺️</view>\r\n\t\t\t\t\t\t\t<text class=\"action-text\">地图定位</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"action-item\" @click=\"startVRExperience\">\r\n\t\t\t\t\t\t\t<view class=\"action-icon vr-icon\">🥽</view>\r\n\t\t\t\t\t\t\t<text class=\"action-text\">VR体验</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"action-item\" @click=\"shareContent\">\r\n\t\t\t\t\t\t\t<view class=\"action-icon share-icon\">📤</view>\r\n\t\t\t\t\t\t\t<text class=\"action-text\">分享推荐</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"action-item\" @click=\"handleCheckIn\">\r\n\t\t\t\t\t\t\t<view class=\"action-icon checkin-icon\" :class=\"{ 'checked-in': content.hasCheckedIn }\">印</view>\r\n\t\t\t\t\t\t\t<text class=\"action-text\">{{ content.hasCheckedIn ? '已打卡' : '盖章打卡' }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\r\n\t\t<!-- 文化详情弹窗 -->\r\n\t\t<view class=\"culture-modal\" v-if=\"showCultureModal\" @click=\"closeCultureModal\">\r\n\t\t\t<view class=\"modal-content\" @click.stop>\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<text class=\"modal-title\">{{ selectedCulture.name }}</text>\r\n\t\t\t\t\t<view class=\"modal-close\" @click=\"closeCultureModal\">×</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-body\">\r\n\t\t\t\t\t<text class=\"modal-text\">{{ selectedCulture.detailDescription }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tprops: {\r\n\t\tcontent: {\r\n\t\t\ttype: Object,\r\n\t\t\trequired: true\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tscrollY: 0,\r\n\t\t\tscrollTop: 0,\r\n\t\t\tactiveSection: -1,\r\n\t\t\tactiveTimelineItem: -1,\r\n\t\t\tactiveCultureCard: -1,\r\n\t\t\tshowNavbar: false,\r\n\t\t\tcompareSliderPosition: 50,\r\n\t\t\tshowCultureModal: false,\r\n\t\t\tselectedCulture: {},\r\n\t\t\ttouchStartY: 0,\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\theaderOffset() { return this.scrollY * 0.5 },\r\n\t\ttitleOffset() { return this.scrollY * 0.3 },\r\n\t\tsubtitleOffset() { return this.scrollY * 0.4 },\r\n\t\ttitleOpacity() { return Math.max(0, 1 - this.scrollY / 300) },\r\n\t\tsubtitleOpacity() { return Math.max(0, 1 - this.scrollY / 200) },\r\n\t\tscrollProgress() { return Math.min(100, (this.scrollY / 2000) * 100) }\r\n\t},\r\n\tmounted() {\r\n\t\tthis.initAnimations();\r\n\t},\r\n\tmethods: {\r\n\t\tinitAnimations() {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.activeSection = 4;\r\n\t\t\t\tthis.activeTimelineItem = 3;\r\n\t\t\t\tthis.activeCultureCard = 3;\r\n\t\t\t}, 500);\r\n\t\t},\r\n\t\tonScroll(e) {\r\n\t\t\tthis.scrollY = e.detail.scrollTop;\r\n\t\t\tthis.showNavbar = this.scrollY > 200;\r\n\t\t},\r\n\t\tonTouchStart(e) { this.touchStartY = e.touches[0].clientY },\r\n\t\tonCompareSlide(e) {\r\n\t\t\tconst touch = e.touches[0];\r\n\t\t\tconst percentage = (touch.clientX / (uni.getSystemInfoSync().windowWidth)) * 100;\r\n\t\t\tthis.compareSliderPosition = Math.max(0, Math.min(100, percentage));\r\n\t\t},\r\n\t\tshowCultureDetail(culture) {\r\n\t\t\tthis.selectedCulture = culture;\r\n\t\t\tthis.showCultureModal = true;\r\n\t\t},\r\n\t\tcloseCultureModal() { this.showCultureModal = false },\r\n\t\tstartVRExperience() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/vr/experience?scenicId=${this.content.id}&name=${encodeURIComponent(this.content.name)}`\r\n\t\t\t});\r\n\t\t},\r\n\t\tshowOnMap() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/map/map?scenicId=${this.content.id}`\r\n\t\t\t});\r\n\t\t},\r\n\t\tshareContent() {\r\n\t\t\tuni.showToast({ title: '分享功能开发中', icon: 'none' });\r\n\t\t},\r\n\t\thandleCheckIn() {\r\n\t\t\tthis.$emit('checkIn', { ...this.content });\r\n\t\t},\r\n\t\tgoBack() {\r\n\t\t\tuni.navigateBack();\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n/* All scenic-specific styles will be here */\r\n@import '../detail.scss';\r\n</style> ", "import Component from 'D:/Users/<USER>/Desktop/yunVr/pages/detail/components/ScenicContent.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AA8NA,MAAK,YAAU;AAAA,EACd,OAAO;AAAA,IACN,SAAS;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,kBAAkB;AAAA,MAClB,iBAAiB,CAAE;AAAA,MACnB,aAAa;AAAA,IACd;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,eAAe;AAAE,aAAO,KAAK,UAAU;AAAA,IAAK;AAAA,IAC5C,cAAc;AAAE,aAAO,KAAK,UAAU;AAAA,IAAK;AAAA,IAC3C,iBAAiB;AAAE,aAAO,KAAK,UAAU;AAAA,IAAK;AAAA,IAC9C,eAAe;AAAE,aAAO,KAAK,IAAI,GAAG,IAAI,KAAK,UAAU,GAAG;AAAA,IAAG;AAAA,IAC7D,kBAAkB;AAAE,aAAO,KAAK,IAAI,GAAG,IAAI,KAAK,UAAU,GAAG;AAAA,IAAG;AAAA,IAChE,iBAAiB;AAAE,aAAO,KAAK,IAAI,KAAM,KAAK,UAAU,MAAQ,GAAG;AAAA,IAAE;AAAA,EACrE;AAAA,EACD,UAAU;AACT,SAAK,eAAc;AAAA,EACnB;AAAA,EACD,SAAS;AAAA,IACR,iBAAiB;AAChB,iBAAW,MAAM;AAChB,aAAK,gBAAgB;AACrB,aAAK,qBAAqB;AAC1B,aAAK,oBAAoB;AAAA,MACzB,GAAE,GAAG;AAAA,IACN;AAAA,IACD,SAAS,GAAG;AACX,WAAK,UAAU,EAAE,OAAO;AACxB,WAAK,aAAa,KAAK,UAAU;AAAA,IACjC;AAAA,IACD,aAAa,GAAG;AAAE,WAAK,cAAc,EAAE,QAAQ,CAAC,EAAE;AAAA,IAAS;AAAA,IAC3D,eAAe,GAAG;AACjB,YAAM,QAAQ,EAAE,QAAQ,CAAC;AACzB,YAAM,aAAc,MAAM,UAAWA,cAAG,MAAC,kBAAmB,EAAC,cAAgB;AAC7E,WAAK,wBAAwB,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,UAAU,CAAC;AAAA,IAClE;AAAA,IACD,kBAAkB,SAAS;AAC1B,WAAK,kBAAkB;AACvB,WAAK,mBAAmB;AAAA,IACxB;AAAA,IACD,oBAAoB;AAAE,WAAK,mBAAmB;AAAA,IAAO;AAAA,IACrD,oBAAoB;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,iCAAiC,KAAK,QAAQ,EAAE,SAAS,mBAAmB,KAAK,QAAQ,IAAI,CAAC;AAAA,MACpG,CAAC;AAAA,IACD;AAAA,IACD,YAAY;AACXA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,2BAA2B,KAAK,QAAQ,EAAE;AAAA,MAChD,CAAC;AAAA,IACD;AAAA,IACD,eAAe;AACdA,oBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAK,CAAG;AAAA,IAChD;AAAA,IACD,gBAAgB;AACf,WAAK,MAAM,WAAW,EAAE,GAAG,KAAK,QAAM,CAAG;AAAA,IACzC;AAAA,IACD,SAAS;AACRA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxSA,GAAG,gBAAgB,SAAS;"}