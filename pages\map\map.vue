<template>
	<view class="map-container">
		<!-- 头部导航栏 -->
		<view class="map-navbar">
			<view class="navbar-content">
				<view class="back-btn" @click="goBack">
					<text class="back-icon">←</text>
				</view>
				<text class="navbar-title">衡阳古韵地图</text>
				<view class="placeholder"></view>
			</view>
		</view>
		

		
		<!-- 地图容器 -->
		<view class="map-wrapper">
			<map
				class="map-view"
				id="hengyangMap"
				:longitude="mapCenter.longitude"
				:latitude="mapCenter.latitude"
				:scale="mapScale"
				layer-style="1"
				:markers="markers"
				@markertap="onMarkerTap"
				show-location
			></map>

			<!-- 地图图例 -->
			<view class="map-legend">
				<view class="legend-title">衡阳景点导览</view>
				<view class="legend-items">
					<view
						v-for="spot in scenicSpots"
						:key="spot.id"
						class="legend-item"
						:class="{ 'active': selectedScenic && selectedScenic.id === spot.id }"
						@click="focusOnScenic(spot.id)"
					>
						<image
							:src="getMarkerIcon(spot.id)"
							class="legend-icon"
							mode="aspectFill"
						></image>
						<text class="legend-name">{{ spot.name }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 景点信息卡片 -->
		<view class="info-card" v-if="selectedScenic" :class="{ 'show': showInfoCard }">
			<view class="card-header">
				<text class="scenic-name">{{ selectedScenic.name }}</text>
				<view class="close-btn" @click="closeInfoCard">
					<text class="close-icon">×</text>
				</view>
			</view>
			<view class="card-content">
				<image :src="selectedScenic.image" mode="aspectFill" class="scenic-image"></image>
				<text class="scenic-desc">{{ selectedScenic.description }}</text>
			</view>
			<view class="card-actions">
				<view class="action-btn primary" @click="goToScenicDetail">
					<text class="btn-text">🏛️ 详细介绍</text>
				</view>
				<view class="action-btn secondary" @click="startVRExperience">
					<text class="btn-text">🥽 VR体验</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// 地图中心点（衡阳市中心）
			mapCenter: {
				longitude: 112.571997,
				latitude: 26.893230
			},
			mapScale: 11,
			showInfoCard: false,
			selectedScenic: null,
			
			// 衡阳六大景点数据
			scenicSpots: [
				{
					id: 1,
					name: '南岳衡山',
					latitude: 27.254167,
					longitude: 112.655833,
					description: '五岳之一，道教佛教圣地，素有"南岳独秀"之美誉',
					image: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanindex.png',
					folder: 'hengshan'
				},
				{
					id: 2,
					name: '石鼓书院',
					latitude: 26.891667,
					longitude: 112.570833,
					description: '中国四大书院之一，千年学府，文化底蕴深厚',
					image: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/shigu/shiguindex.png',
					folder: 'shigu'
				},
				{
					id: 3,
					name: '回雁峰',
					latitude: 26.883333,
					longitude: 112.583333,
					description: '衡阳八景之首，"北雁南飞，至此歇翅停回"',
					image: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/huiyan/huiyanindex.jpg',
					folder: 'huiyan'
				},
				{
					id: 4,
					name: '南华大学',
					latitude: 26.906667,
					longitude: 112.613333,
					description: '现代教育文化地标，培育英才的知识殿堂',
					image: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/nanhua/nanhuaindex.png',
					folder: 'nanhua'
				},
				{
					id: 5,
					name: '东洲岛',
					latitude: 26.888889,
					longitude: 112.575000,
					description: '湘江中的文化岛屿，诗意盎然的江心绿洲',
					image: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/dongzhou/dongzhouindex.jpg',
					folder: 'dongzhou'
				},
				{
					id: 6,
					name: '岣嵝峰',
					latitude: 26.916667,
					longitude: 112.550000,
					description: '传统建筑文化代表，古韵悠长的文化名山',
					image: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/goulou/goullouindex.png',
					folder: 'goulou'
				}
			],
			
			// 地图标记点
			markers: []
		}
	},
	
	onLoad(options) {
		// 如果从首页传来了景点ID，则定位到该景点
		if (options.scenicId) {
			this.focusOnScenic(parseInt(options.scenicId))
		}
		this.initMarkers()
	},
	
	methods: {
		// 初始化地图标记
		initMarkers() {
			this.markers = this.scenicSpots.map(spot => ({
				id: spot.id,
				latitude: spot.latitude,
				longitude: spot.longitude,
				width: 60,
				height: 60,
				iconPath: this.createCustomMarker(spot),
				callout: {
					content: spot.name,
					color: '#8B4513',
					fontSize: 24,
					borderRadius: 12,
					bgColor: 'rgba(255, 248, 220, 0.95)',
					padding: 12,
					display: 'ALWAYS',
					textAlign: 'center',
					borderWidth: 2,
					borderColor: 'rgba(139, 69, 19, 0.3)',
					// 设置callout位置
					anchorY: -10,
					anchorX: 0
				}
			}))
		},

		// 创建自定义标记
		createCustomMarker(spot) {
			// 使用圆形的标记图标，提供更好的视觉效果
			const iconMap = {
				1: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanmark.jpg', // 南岳衡山
				2: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/shigu/shigumark.jpg',       // 石鼓书院
				3: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/huiyan/huiyanmark.jpg',     // 回雁峰
				4: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/nanhua/nanhuamark.jpg',     // 南华大学
				5: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/dongzhou/dongzhoumark.jpg', // 东洲岛
				6: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/goulou/gouloumark.jpg'      // 岣嵝峰
			}
			return iconMap[spot.id] || 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanmark.jpg'
		},

		// 获取标记图标（保留兼容性）
		getMarkerIcon(scenicId) {
			const iconMap = {
				1: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanmark.jpg', // 南岳衡山
				2: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/shigu/shigumark.jpg',       // 石鼓书院
				3: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/huiyan/huiyanmark.jpg',     // 回雁峰
				4: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/nanhua/nanhuamark.jpg',     // 南华大学
				5: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/dongzhou/dongzhoumark.jpg', // 东洲岛
				6: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/goulou/gouloumark.jpg'      // 岣嵝峰
			}
			return iconMap[scenicId] || 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanmark.jpg'
		},
		
		// 标记点击事件
		onMarkerTap(e) {
			const markerId = e.markerId || e.detail.markerId
			const scenic = this.scenicSpots.find(spot => spot.id === markerId)
			if (scenic) {
				this.selectedScenic = scenic
				this.showInfoCard = true

				// 移动地图中心到选中的景点
				this.mapCenter = {
					longitude: scenic.longitude,
					latitude: scenic.latitude
				}
				this.mapScale = 14
			}
		},

		// 自定义标记点击事件
		onCustomMarkerTap(spot) {
			this.selectedScenic = spot
			this.showInfoCard = true

			// 移动地图中心到选中的景点
			this.mapCenter = {
				longitude: spot.longitude,
				latitude: spot.latitude
			}
			this.mapScale = 14
		},

		// 获取标记在屏幕上的位置（简化版本）
		getMarkerPosition(spot) {
			// 注意：这是一个简化的实现
			// 实际项目中建议使用地图API提供的坐标转换方法
			return {
				display: 'none' // 暂时隐藏，使用原生markers
			}
		},
		
		// 聚焦到指定景点
		focusOnScenic(scenicId) {
			const scenic = this.scenicSpots.find(spot => spot.id === scenicId)
			if (scenic) {
				this.mapCenter = {
					longitude: scenic.longitude,
					latitude: scenic.latitude
				}
				this.mapScale = 14
				this.selectedScenic = scenic
				this.showInfoCard = true
			}
		},
		
		// 关闭信息卡片
		closeInfoCard() {
			this.showInfoCard = false
			this.selectedScenic = null
			// 恢复地图全景
			this.mapCenter = {
				longitude: 112.571997,
				latitude: 26.893230
			}
			this.mapScale = 11
		},
		
		// 跳转到景点详情
		goToScenicDetail() {
			if (this.selectedScenic) {
				uni.navigateTo({
					url: `/pages/scenic/detail?id=${this.selectedScenic.id}`
				})
			}
		},
		
		// 开始VR体验
		startVRExperience() {
			if (this.selectedScenic) {
				console.log('开始VR体验:', this.selectedScenic)

				// 跳转到VR体验页面
				uni.navigateTo({
					url: `/pages/vr/experience?scenicId=${this.selectedScenic.id}&name=${encodeURIComponent(this.selectedScenic.name)}`
				})
			} else {
				uni.showToast({
					title: '请先选择一个景点',
					icon: 'none'
				})
			}
		},
		
		// 返回首页
		goBack() {
			uni.reLaunch({
				url: '/pages/index/index'
			})
		}
	}
}
</script>

<style lang="scss">
.map-container {
	width: 100vw;
	height: 100vh;
	position: relative;
	overflow: hidden;
}

.map-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(139, 69, 19, 0.9);
	backdrop-filter: blur(10rpx);
	
	.navbar-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		padding-top: calc(20rpx + var(--status-bar-height, 0));
		
		.back-btn {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 50%;
			
			.back-icon {
				font-size: 32rpx;
				color: #FFF8DC;
				font-weight: bold;
			}
		}
		
		.navbar-title {
			font-size: 36rpx;
			color: #FFD700;
			font-weight: bold;
			text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5);
		}
		
		.placeholder {
			width: 60rpx;
		}
	}
}



.map-wrapper {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 2;

	.map-view {
		width: 100%;
		height: 100%;
	}

	// 地图图例
	.map-legend {
		position: absolute;
		top: 120rpx;
		right: 20rpx;
		background: rgba(255, 248, 220, 0.95);
		border-radius: 20rpx;
		padding: 20rpx;
		box-shadow: 0 8rpx 24rpx rgba(139, 69, 19, 0.25);
		backdrop-filter: blur(10rpx);
		border: 2rpx solid rgba(139, 69, 19, 0.2);
		z-index: 100;
		max-width: 180rpx;

		.legend-title {
			font-size: 28rpx;
			color: #8B4513;
			font-weight: 700;
			text-align: center;
			margin-bottom: 20rpx;
			text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
		}

		.legend-items {
			display: flex;
			flex-direction: column;
			gap: 12rpx;

			.legend-item {
				display: flex;
				align-items: center;
				padding: 12rpx;
				border-radius: 12rpx;
				transition: all 0.3s ease;
				cursor: pointer;

				&:hover {
					background: rgba(139, 69, 19, 0.1);
					transform: translateX(4rpx);
				}

				&.active {
					background: rgba(139, 69, 19, 0.2);
					border: 2rpx solid #D2691E;
					transform: scale(1.05);
				}

				.legend-icon {
					width: 32rpx;
					height: 32rpx;
					border-radius: 50%;
					margin-right: 12rpx;
					border: 2rpx solid rgba(139, 69, 19, 0.3);
					flex-shrink: 0;
				}

				.legend-name {
					font-size: 22rpx;
					color: #8B4513;
					font-weight: 600;
					line-height: 1.2;
					text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
					flex: 1;
				}
			}
		}
	}
}

.info-card {
	position: fixed;
	bottom: -400rpx;
	left: 30rpx;
	right: 30rpx;
	background: rgba(255, 248, 220, 0.95);
	border-radius: 20rpx;
	box-shadow: 0 10rpx 30rpx rgba(139, 69, 19, 0.3);
	z-index: 1001;
	transition: bottom 0.3s ease-in-out;
	backdrop-filter: blur(10rpx);
	
	&.show {
		bottom: 30rpx;
	}
	
	.card-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 2rpx solid rgba(139, 69, 19, 0.1);
		
		.scenic-name {
			font-size: 36rpx;
			color: #8B4513;
			font-weight: bold;
		}
		
		.close-btn {
			width: 50rpx;
			height: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(139, 69, 19, 0.1);
			border-radius: 50%;
			
			.close-icon {
				font-size: 32rpx;
				color: #8B4513;
				font-weight: bold;
			}
		}
	}
	
	.card-content {
		padding: 30rpx;
		
		.scenic-image {
			width: 100%;
			height: 200rpx;
			border-radius: 12rpx;
			margin-bottom: 20rpx;
		}
		
		.scenic-desc {
			font-size: 28rpx;
			color: #654321;
			line-height: 1.6;
		}
	}
	
	.card-actions {
		display: flex;
		gap: 20rpx;
		padding: 0 30rpx 30rpx;
		
		.action-btn {
			flex: 1;
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 40rpx;
			
			&.primary {
				background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
				
				.btn-text {
					color: #FFF8DC;
				}
			}
			
			&.secondary {
				background: rgba(139, 69, 19, 0.1);
				border: 2rpx solid #8B4513;
				
				.btn-text {
					color: #8B4513;
				}
			}
			
			.btn-text {
				font-size: 28rpx;
				font-weight: bold;
			}
		}
	}
}
</style>
