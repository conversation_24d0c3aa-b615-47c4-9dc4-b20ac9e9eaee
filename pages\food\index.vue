<template>
	<view class="food-gallery-page">
		<!-- 顶部画廊 -->
		<view class="header-gallery" :style="{ height: headerHeight + 'px' }">
			<image :src="coverImage" class="header-bg" mode="aspectFill"></image>
			<view class="header-overlay"></view>
			<view class="header-content">
				<view class="back-button" @click="goBack">
					<text class="back-icon">‹</text>
				</view>
				<view class="title-container" :style="{ opacity: titleOpacity }">
					<text class="main-title">衡阳风味</text>
					<text class="subtitle">舌尖上的古城记忆</text>
				</view>
			</view>
		</view>

		<!-- 美食瀑布流 -->
		<scroll-view :scroll-y="true" class="waterfall-container" @scroll="onScroll">
			<view class="waterfall-content">
				<view class="column">
					<view v-for="item in column1" :key="item.key" class="food-card" @click="showFoodDetail(item)">
						<image :src="item.image" mode="widthFix" class="food-image"></image>
						<view class="card-info">
							<text class="food-name">{{ item.name }}</text>
						</view>
					</view>
				</view>
				<view class="column">
					<view v-for="item in column2" :key="item.key" class="food-card" @click="showFoodDetail(item)">
						<image :src="item.image" mode="widthFix" class="food-image"></image>
						<view class="card-info">
							<text class="food-name">{{ item.name }}</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import { FOOD_IMAGES } from '@/common/config.js';

export default {
	data() {
		return {
			coverImage: FOOD_IMAGES.index,
			allFood: [],
			column1: [],
			column2: [],
			scrollTop: 0,
			initialHeaderHeight: 250,
		};
	},
	computed: {
		headerHeight() {
			const newHeight = this.initialHeaderHeight - this.scrollTop;
			return Math.max(120, newHeight); // 增加最小高度，确保标题可见
		},
		titleOpacity() {
			return Math.max(0, 1 - (this.scrollTop / 100));
		}
	},
	onLoad() {
		this.loadFoodData();
		this.distributeFood();
	},
	methods: {
		loadFoodData() {
			this.allFood = [
				{ key: 'xiaogurou', name: '青椒剐骨肉', image: FOOD_IMAGES.xiaogurou },
				{ key: 'yufen', name: '衡阳鱼粉', image: FOOD_IMAGES.yufen },
				{ key: 'tutouwan', name: '衡阳土头碗', image: FOOD_IMAGES.tutouwan },
				{ key: 'cuidu', name: '衡东脆肚', image: FOOD_IMAGES.cuidu },
				{ key: 'tuji', name: '茶油炒土鸡', image: FOOD_IMAGES.tuji },
				{ key: 'fuzirou', name: '杨桥麸子肉', image: FOOD_IMAGES.fuzirou },
			].sort(() => Math.random() - 0.5); // 随机排序增加趣味性
		},
		distributeFood() {
			// 手动实现瀑布流分列，更可靠
			this.allFood.forEach((food, index) => {
				if (index % 2 === 0) {
					this.column1.push(food);
				} else {
					this.column2.push(food);
				}
			});
		},
		onScroll(e) {
			this.scrollTop = e.detail.scrollTop;
		},
		showFoodDetail(item) {
			uni.showToast({
				title: `正在品尝 ${item.name}`,
				icon: 'none'
			});
			// 未来可跳转到美食详情页
			uni.navigateTo({
				url: `/pages/food/detail?id=${item.key}`
			});
		},
		goBack() {
			uni.navigateBack();
		}
	}
};
</script>

<style lang="scss">
.food-gallery-page {
	height: 100vh;
	width: 100vw;
	background-color: #2c2828;
	overflow: hidden;
	position: relative;
}

.header-gallery {
	width: 100%;
	position: fixed; /* 改为固定定位 */
	top: 0;
	left: 0;
	z-index: 10;
	transition: height 0.2s ease-out;

	.header-bg {
		position: absolute;
		top: 0; left: 0;
		width: 100%; height: 100%;
		object-fit: cover;
	}
	.header-overlay {
		position: absolute;
		top: 0; left: 0;
		width: 100%; height: 100%;
		background: linear-gradient(to top, rgba(26,26,26,1) 0%, rgba(26,26,26,0.6) 50%, transparent 100%);
	}
	.header-content {
		position: absolute;
		bottom: 0;
		width: 100%;
		padding: 40rpx;
		box-sizing: border-box;
		text-align: center;
	}
	.back-button {
		position: absolute;
		top: var(--status-bar-height);
		left: 20rpx;
		width: 80rpx; height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		.back-icon { font-size: 50rpx; color: #fff; }
	}
	.title-container {
		transition: opacity 0.2s ease-out;
	}
	.main-title {
		font-size: 52rpx;
		font-weight: bold;
		color: #FFD700;
		display: block;
	}
	.subtitle {
		font-size: 28rpx;
		color: #e0e0e0;
	}
}

.waterfall-container {
	width: 100%;
	height: 100%; /* 占满整个屏幕 */
	padding-top: 250px; /* 初始顶部画廊高度 */
	box-sizing: border-box;
}

.waterfall-content {
	display: flex;
	padding: 20rpx;
	padding-bottom: 60rpx; /* 增加底部空间 */
	box-sizing: border-box;

	.column {
		width: 50%;
		padding: 0 15rpx; /* 增加列间距 */
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		gap: 30rpx; /* 增加卡片垂直间距 */
	}
	
	.food-card {
		width: 100%;
		border-radius: 20rpx; /* 更大的圆角 */
		overflow: hidden;
		background-color: #3a3a3e;
		box-shadow: 0 10rpx 25rpx rgba(0,0,0,0.7);
		break-inside: avoid;
		position: relative;
		
		.food-image {
			width: 100%;
			display: block;
		}
		
		.card-info {
			position: absolute;
			bottom: 0; left: 0; right: 0;
			padding: 40rpx 20rpx 20rpx;
			background: linear-gradient(to top, rgba(0,0,0,0.9), transparent);
			
			.food-name {
				font-size: 30rpx;
				color: #fff;
				font-weight: bold;
				text-shadow: 1rpx 1rpx 3rpx rgba(0,0,0,0.5);
			}
		}
	}
}
</style> 