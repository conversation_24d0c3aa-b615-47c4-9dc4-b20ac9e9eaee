{"version": 3, "file": "index.js", "sources": ["pages/discover/index.vue", "../../../../App/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZGlzY292ZXIvaW5kZXgudnVl"], "sourcesContent": ["<template>\n\t<view class=\"discover-page\" v-if=\"featuredSpot.image\">\n\t\t<!-- 全局背景 -->\n\t\t<view class=\"page-bg\" :style=\"{ backgroundImage: `url(${featuredSpot.image})` }\"></view>\n\t\t<view class=\"page-bg-overlay\"></view>\n\n\t\t<view class=\"discover-container\">\n\t\t\t<!-- 返回按钮 -->\n\t\t\t<view class=\"back-button\" @click=\"goBack\">\n\t\t\t\t<view class=\"arrow\"></view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 顶部景点卡片 -->\n\t\t\t<view \n\t\t\t\tclass=\"featured-card\" \n\t\t\t\t:style=\"{ \n\t\t\t\t\tbackgroundImage: `url(${featuredSpot.image})`,\n\t\t\t\t\tbackgroundPosition: featuredSpot.id === 2 ? 'center 20%' : 'center'\n\t\t\t\t}\"\n\t\t\t>\n\t\t\t\t<view class=\"card-overlay\"></view>\n\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t<text class=\"card-title\">{{ featuredSpot.name }}</text>\n\t\t\t\t\t<text class=\"card-subtitle\">{{ featuredSpot.subtitle }}</text>\n\t\t\t\t\t<view class=\"card-buttons\">\n\t\t\t\t\t\t<button class=\"card-btn detail\" @click=\"goToDetail\">查看详情</button>\n\t\t\t\t\t\t<button class=\"card-btn vr\" @click=\"goToVR\">VR体验</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- AI 对话区域 -->\n\t\t\t<view class=\"ai-chat-container\">\n\t\t\t\t<scroll-view :scroll-y=\"true\" class=\"chat-history\" :scroll-top=\"scrollTop\">\n\t\t\t\t\t<view v-for=\"(message, index) in chatHistory\" :key=\"index\" class=\"message-wrapper\" :class=\"[message.role]\">\n\t\t\t\t\t\t<view class=\"avatar\" :class=\"[message.role]\">{{ message.role === 'assistant' ? '通' : '我' }}</view>\n\t\t\t\t\t\t<view class=\"message-content\">\n\t\t\t\t\t\t\t<text :user-select=\"true\">{{ message.content }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"isLoading\" class=\"message-wrapper assistant\">\n\t\t\t\t\t\t<view class=\"avatar assistant\">通</view>\n\t\t\t\t\t\t<view class=\"message-content loading\">\n\t\t\t\t\t\t\t<view class=\"dot-flashing\"></view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\n\t\t\t\t<view class=\"input-area\">\n\t\t\t\t\t<input v-model=\"userInput\" type=\"text\" placeholder=\"聊聊衡阳的风土人情...\" class=\"input-field\" :disabled=\"isLoading\" @confirm=\"sendMessage\" />\n\t\t\t\t\t<button @click=\"sendMessage\" class=\"send-button\" :disabled=\"isLoading || !userInput\">发送</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport config from '@/api/config.js';\nimport { SCENIC_IMAGES } from '@/common/config.js';\n\nconst SYSTEM_PROMPT_TEMPLATE = (spotName) => `你是一位热情、博学、风趣的衡阳本地导游\"衡阳通\"。你的任务是：\n1.  **角色扮演**: 以\"衡阳通\"的身份，用自然、欢迎的语气与用户对话。\n2.  **开场白**: 你的第一句话必须是：\"欢迎来到${spotName}！这里是衡阳的瑰宝之一。除了眼前的美景，你还想了解衡阳的哪些风土人情呢？无论是嗦螺的来历，还是王船山的逸闻，我都可以为你娓娓道来。\"\n3.  **知识范围**: 你是衡阳的万事通。你的知识不限于我给你提供的这个初始景点。你可以自由介绍衡阳的任何方面，包括但不限于：地方美食（如鱼粉、唆螺）、历史名人（如蔡伦、王夫之）、方言文化、未收录的小众景点、民俗节庆等。\n4.  **互动风格**: 主动引导对话，激发用户的好奇心。当用户的问题超出你的知识范围时，要礼貌地承认并尝试从其他角度提供相关信息。\n5.  **核心要求**: 保持对话的趣味性和互动性，让用户感觉在和一位真实的衡阳朋友聊天。`;\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tfeaturedSpot: {},\n\t\t\tmessages: [],\n\t\t\tuserInput: '',\n\t\t\tisLoading: false,\n\t\t\tscrollTop: 0\n\t\t};\n\t},\n\tcomputed: {\n\t\tchatHistory() {\n\t\t\treturn this.messages.filter(m => m.role !== 'system');\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tthis.loadFeaturedSpot(options.id);\n\t\tthis.startChat();\n\t},\n\tmethods: {\n\t\tloadFeaturedSpot(id) {\n\t\t\tconst allSpots = {\n\t\t\t\t1: { id: 1, name: '南岳衡山', subtitle: '五岳独秀，禅宗圣地', folder: 'hengshan' },\n\t\t\t\t2: { id: 2, name: '石鼓书院', subtitle: '千年学府，理学源头', folder: 'shigu' },\n\t\t\t\t3: { id: 3, name: '回雁峰', subtitle: '雁城之巅，俯瞰湘江', folder: 'huiyan' },\n\t\t\t\t4: { id: 4, name: '南华大学', subtitle: '核学府，育人才', folder: 'nanhua' },\n\t\t\t\t5: { id: 5, name: '东洲岛', subtitle: '江心绿洲，城市氧吧', folder: 'dongzhou' },\n\t\t\t\t6: { id: 6, name: '岣嵝峰', subtitle: '神秘禹碑，千古之谜', folder: 'goulou' }\n\t\t\t};\n\t\t\tconst spotData = allSpots[id] || allSpots[1]; // Fallback to a default\n\t\t\tthis.featuredSpot = {\n\t\t\t\t...spotData,\n\t\t\t\timage: SCENIC_IMAGES[spotData.folder].view\n\t\t\t};\n\t\t},\n\t\tstartChat() {\n\t\t\tthis.messages = [\n\t\t\t\t{ role: 'system', content: SYSTEM_PROMPT_TEMPLATE(this.featuredSpot.name) },\n\t\t\t\t{ role: 'assistant', content: `欢迎来到${this.featuredSpot.name}！这里是衡阳的瑰宝之一。除了眼前的美景，你还想了解衡阳的哪些风土人情呢？无论是嗦螺的来历，还是王船山的逸闻，我都可以为你娓娓道来。` }\n\t\t\t];\n\t\t},\n\t\tsendMessage() {\n\t\t\tif (!this.userInput.trim() || this.isLoading) return;\n\t\t\tconst userMessage = { role: 'user', content: this.userInput.trim() };\n\t\t\tthis.messages = [...this.messages, userMessage];\n\t\t\tthis.userInput = '';\n\t\t\tthis.fetchAIResponse();\n\t\t},\n\t\tasync fetchAIResponse() {\n\t\t\tthis.isLoading = true;\n\t\t\tthis.scrollToBottom();\n\n\t\t\t// 确保总是包含一个system角色的消息\n\t\t\tconst apiMessages = this.messages.filter(msg => msg.role !== 'system');\n\t\t\tapiMessages.unshift({ role: 'system', content: SYSTEM_PROMPT_TEMPLATE(this.featuredSpot.name) });\n\n\t\t\ttry {\n\t\t\t\tconst res = await uni.request({\n\t\t\t\t\turl: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',\n\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\theader: {\n\t\t\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t\t\t'Authorization': `Bearer ${config.ZHIPU_API_KEY}`\n\t\t\t\t\t},\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tmodel: 'glm-4',\n\t\t\t\t\t\tmessages: apiMessages,\n\t\t\t\t\t\tstream: false\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tif (res.statusCode === 200 && res.data.choices) {\n\t\t\t\t\tthis.messages.push(res.data.choices[0].message);\n\t\t\t\t} else {\n\t\t\t\t\tthis.handleError(res.data);\n\t\t\t\t}\n\t\t\t} catch (err) {\n\t\t\t\tthis.handleError(err);\n\t\t\t} finally {\n\t\t\t\tthis.isLoading = false;\n\t\t\t\tthis.scrollToBottom();\n\t\t\t}\n\t\t},\n\t\thandleError(error) {\n\t\t\tconsole.error('AI请求失败:', error);\n\t\t\tthis.messages.push({\n\t\t\t\trole: 'assistant',\n\t\t\t\tcontent: '抱歉，导览线路有些繁忙，请稍后再试。'\n\t\t\t});\n\t\t},\n\t\tgoBack() {\n\t\t\tuni.navigateBack();\n\t\t},\n\t\tgoToDetail() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/scenic/detail?id=${this.featuredSpot.id}`\n\t\t\t});\n\t\t},\n\t\tgoToVR() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/vr/experience?scenicId=${this.featuredSpot.id}`\n\t\t\t});\n\t\t},\n\t\tscrollToBottom() {\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.scrollTop = this.messages.length * 1000;\n\t\t\t});\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\">\n@keyframes slide-up-fade-in {\n\tfrom {\n\t\topacity: 0;\n\t\ttransform: translateY(20rpx);\n\t}\n\tto {\n\t\topacity: 1;\n\t\ttransform: translateY(0);\n\t}\n}\n\n@keyframes breath {\n\tfrom {\n\t\ttransform: scale(1);\n\t\tbox-shadow: 0 0 5rpx rgba(0,0,0,0.3);\n\t}\n\tto {\n\t\ttransform: scale(1.05);\n\t\tbox-shadow: 0 0 15rpx rgba(255, 215, 0, 0.4); /* 暗金辉光 */\n\t}\n}\n\n.discover-page {\n\theight: 100vh;\n\twidth: 100vw;\n\toverflow: hidden;\n\tposition: relative;\n}\n\n.page-bg {\n\tposition: absolute;\n\ttop: -5%;\n\tleft: -5%;\n\twidth: 110%;\n\theight: 110%;\n\tfilter: blur(40px) brightness(0.7);\n\tz-index: 1;\n\tbackground-size: cover;\n\tbackground-position: center;\n}\n\n.page-bg-overlay {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tbackground-color: rgba(0, 0, 0, 0.2);\n\tz-index: 2;\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\n}\n\n.discover-container {\n\tposition: relative;\n\tz-index: 3;\n\tdisplay: flex;\n\tflex-direction: column;\n\theight: 100vh;\n\tbackground-color: transparent;\n\t/* position: relative; */ /* 已被 z-index 包含 */\n}\n\n.back-button {\n\tposition: absolute;\n\ttop: calc(var(--status-bar-height) + 20rpx);\n\tleft: 30rpx;\n\tz-index: 99;\n\twidth: 70rpx;\n\theight: 70rpx;\n\tbackground-color: rgba(0, 0, 0, 0.4);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tbackdrop-filter: blur(5px);\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\n}\n\n.arrow {\n\twidth: 20rpx;\n\theight: 20rpx;\n\tborder-left: 4rpx solid white;\n\tborder-bottom: 4rpx solid white;\n\ttransform: translateX(2rpx) rotate(45deg);\n}\n\n.featured-card {\n\theight: 40vh;\n\tposition: relative;\n\tcolor: #fff;\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: flex-end;\n\tbackground-size: cover;\n\tbackground-position: center;\n\t\n\t.card-overlay {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: linear-gradient(to top, rgba(0,0,0,0.8) 0%, transparent 60%);\n\t\tz-index: 2;\n\t}\n\n\t.card-content {\n\t\tposition: relative;\n\t\tz-index: 3;\n\t\tpadding: 40rpx;\n\t}\n\n\t.card-title {\n\t\tfont-size: 52rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: block;\n\t\ttext-shadow: 2rpx 2rpx 4rpx rgba(0,0,0,0.5);\n\t}\n\n\t.card-subtitle {\n\t\tfont-size: 28rpx;\n\t\topacity: 0.9;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.card-buttons {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t}\n\n\t.card-btn {\n\t\tflex: 1;\n\t\tbackground: rgba(255, 255, 255, 0.2);\n\t\tbackdrop-filter: blur(5px);\n\t\tcolor: #fff;\n\t\tborder-radius: 40rpx;\n\t\tfont-size: 28rpx;\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.3);\n\n\t\t&.vr {\n\t\t\tbackground: #FFD700;\n\t\t\tcolor: #333;\n\t\t\tborder: none;\n\t\t}\n\t}\n}\n\n.ai-chat-container {\n\theight: 60vh;\n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground-color: #2a2a2e;\n\toverflow: hidden;\n}\n\n.chat-history {\n\tflex: 1;\n\tpadding: 20rpx;\n\tbox-sizing: border-box;\n\toverflow-y: auto;\n}\n\n.message-wrapper {\n\tdisplay: flex;\n\tmargin-bottom: 30rpx;\n\tmax-width: 85%;\n\tanimation: slide-up-fade-in 0.5s ease-out forwards;\n\t\n\t&.user {\n\t\tflex-direction: row-reverse;\n\t\tmargin-left: auto;\n\t}\n\n\t.avatar {\n\t\twidth: 70rpx;\n\t\theight: 70rpx;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-weight: bold;\n\t\tflex-shrink: 0;\n\t\tbackground: #444;\n\t\tcolor: #fff;\n\t\t\n\t\t&.user {\n\t\t\tmargin-left: 20rpx;\n\t\t\tbackground-color: #a98e72;\n\t\t\tcolor: #1a1616;\n\t\t\tborder-color: #d4b899;\n\t\t\tbox-shadow: 0 0 10rpx rgba(212, 184, 153, 0.3);\n\t\t}\n\t\t&.assistant {\n\t\t\tmargin-right: 20rpx;\n\t\t\tbackground-color: #4a423b; /* 更深的暗金背景 */\n\t\t\tcolor: #FFD700; /* 金色字体 */\n\t\t\tborder-color: #6a5f55; /* 深金色边框 */\n\t\t\tanimation: breath 2.5s ease-in-out infinite alternate;\n\t\t}\n\t}\n\t\n\t.message-content {\n\t\tpadding: 20rpx;\n\t\tborder-radius: 24rpx;\n\t\tfont-size: 28rpx;\n\t\tline-height: 1.6;\n\t\tbackground: rgba(255, 255, 255, 0.1);\n\t\tcolor: #e0e0e0;\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\n\t\tbackdrop-filter: blur(10px);\n\t\t\n\t\t&.loading {\n\t\t\tbackground: rgba(255, 255, 255, 0.1);\n\t\t\tbackdrop-filter: blur(10px);\n\t\t\t.dot-flashing {\n\t\t\t\tposition: relative;\n\t\t\t\twidth: 8rpx;\n\t\t\t\theight: 8rpx;\n\t\t\t\tborder-radius: 5rpx;\n\t\t\t\tbackground-color: #888;\n\t\t\t\tcolor: #888;\n\t\t\t\tanimation: dotFlashing 1s infinite linear alternate;\n\t\t\t\tanimation-delay: .5s;\n\t\t\t\t&::before, &::after { content: ''; display: inline-block; position: absolute; top: 0; }\n\t\t\t\t&::before { left: -15rpx; width: 8rpx; height: 8rpx; border-radius: 5rpx; background-color: #888; color: #888; animation: dotFlashing 1s infinite alternate; animation-delay: 0s; }\n\t\t\t\t&::after { left: 15rpx; width: 8rpx; height: 8rpx; border-radius: 5rpx; background-color: #888; color: #888; animation: dotFlashing 1s infinite alternate; animation-delay: 1s; }\n\t\t\t}\n\t\t}\n\t}\n}\n\n.input-area {\n\tdisplay: flex;\n\tpadding: 20rpx;\n\tbackground-color: #1c1c1e;\n\tborder-top: 1rpx solid #444;\n}\n\n.input-field {\n\tflex: 1;\n\theight: 80rpx;\n\tpadding: 0 30rpx;\n\tbackground-color: rgba(0, 0, 0, 0.2);\n\tbackdrop-filter: blur(10px);\n\tborder-radius: 40rpx;\n\tfont-size: 28rpx;\n\tcolor: #fff;\n\tmargin-right: 20rpx;\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\n}\n\n.send-button {\n\tbackground-color: #FFD700;\n\tcolor: #333;\n\tborder-radius: 40rpx;\n\tborder: none;\n\t&[disabled] { \n\t\tbackground-color: rgba(255, 255, 255, 0.1);\n\t\tcolor: #999; \n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\n\t}\n}\n\n@keyframes dotFlashing {\n\t0% { background-color: #888; }\n\t50%, 100% { background-color: #444; }\n}\n</style> ", "import MiniProgramPage from 'D:/Users/<USER>/Desktop/yunVr/pages/discover/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["SCENIC_IMAGES", "uni", "config"], "mappings": ";;;;AA6DA,MAAM,yBAAyB,CAAC,aAAa;AAAA;AAAA,8BAEf,QAAQ;AAAA;AAAA;AAAA;AAKtC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,cAAc,CAAE;AAAA,MAChB,UAAU,CAAE;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA;EAEZ;AAAA,EACD,UAAU;AAAA,IACT,cAAc;AACb,aAAO,KAAK,SAAS,OAAO,OAAK,EAAE,SAAS,QAAQ;AAAA,IACrD;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,SAAK,iBAAiB,QAAQ,EAAE;AAChC,SAAK,UAAS;AAAA,EACd;AAAA,EACD,SAAS;AAAA,IACR,iBAAiB,IAAI;AACpB,YAAM,WAAW;AAAA,QAChB,GAAG,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,aAAa,QAAQ,WAAY;AAAA,QACrE,GAAG,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,aAAa,QAAQ,QAAS;AAAA,QAClE,GAAG,EAAE,IAAI,GAAG,MAAM,OAAO,UAAU,aAAa,QAAQ,SAAU;AAAA,QAClE,GAAG,EAAE,IAAI,GAAG,MAAM,QAAQ,UAAU,WAAW,QAAQ,SAAU;AAAA,QACjE,GAAG,EAAE,IAAI,GAAG,MAAM,OAAO,UAAU,aAAa,QAAQ,WAAY;AAAA,QACpE,GAAG,EAAE,IAAI,GAAG,MAAM,OAAO,UAAU,aAAa,QAAQ,SAAS;AAAA;AAElE,YAAM,WAAW,SAAS,EAAE,KAAK,SAAS,CAAC;AAC3C,WAAK,eAAe;AAAA,QACnB,GAAG;AAAA,QACH,OAAOA,cAAa,cAAC,SAAS,MAAM,EAAE;AAAA;IAEvC;AAAA,IACD,YAAY;AACX,WAAK,WAAW;AAAA,QACf,EAAE,MAAM,UAAU,SAAS,uBAAuB,KAAK,aAAa,IAAI,EAAG;AAAA,QAC3E,EAAE,MAAM,aAAa,SAAS,OAAO,KAAK,aAAa,IAAI,oEAAoE;AAAA;IAEhI;AAAA,IACD,cAAc;AACb,UAAI,CAAC,KAAK,UAAU,KAAI,KAAM,KAAK;AAAW;AAC9C,YAAM,cAAc,EAAE,MAAM,QAAQ,SAAS,KAAK,UAAU,KAAI;AAChE,WAAK,WAAW,CAAC,GAAG,KAAK,UAAU,WAAW;AAC9C,WAAK,YAAY;AACjB,WAAK,gBAAe;AAAA,IACpB;AAAA,IACD,MAAM,kBAAkB;AACvB,WAAK,YAAY;AACjB,WAAK,eAAc;AAGnB,YAAM,cAAc,KAAK,SAAS,OAAO,SAAO,IAAI,SAAS,QAAQ;AACrE,kBAAY,QAAQ,EAAE,MAAM,UAAU,SAAS,uBAAuB,KAAK,aAAa,IAAI,EAAG,CAAA;AAE/F,UAAI;AACH,cAAM,MAAM,MAAMC,cAAG,MAAC,QAAQ;AAAA,UAC7B,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,QAAQ;AAAA,YACP,gBAAgB;AAAA,YAChB,iBAAiB,UAAUC,WAAM,OAAC,aAAa;AAAA,UAC/C;AAAA,UACD,MAAM;AAAA,YACL,OAAO;AAAA,YACP,UAAU;AAAA,YACV,QAAQ;AAAA,UACT;AAAA,QACD,CAAC;AAED,YAAI,IAAI,eAAe,OAAO,IAAI,KAAK,SAAS;AAC/C,eAAK,SAAS,KAAK,IAAI,KAAK,QAAQ,CAAC,EAAE,OAAO;AAAA,eACxC;AACN,eAAK,YAAY,IAAI,IAAI;AAAA,QAC1B;AAAA,MACC,SAAO,KAAK;AACb,aAAK,YAAY,GAAG;AAAA,MACrB,UAAU;AACT,aAAK,YAAY;AACjB,aAAK,eAAc;AAAA,MACpB;AAAA,IACA;AAAA,IACD,YAAY,OAAO;AAClBD,oBAAc,MAAA,MAAA,SAAA,mCAAA,WAAW,KAAK;AAC9B,WAAK,SAAS,KAAK;AAAA,QAClB,MAAM;AAAA,QACN,SAAS;AAAA,MACV,CAAC;AAAA,IACD;AAAA,IACD,SAAS;AACRA,oBAAG,MAAC,aAAY;AAAA,IAChB;AAAA,IACD,aAAa;AACZA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,2BAA2B,KAAK,aAAa,EAAE;AAAA,MACrD,CAAC;AAAA,IACD;AAAA,IACD,SAAS;AACRA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,iCAAiC,KAAK,aAAa,EAAE;AAAA,MAC3D,CAAC;AAAA,IACD;AAAA,IACD,iBAAiB;AAChB,WAAK,UAAU,MAAM;AACpB,aAAK,YAAY,KAAK,SAAS,SAAS;AAAA,MACzC,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChLA,GAAG,WAAW,eAAe;"}