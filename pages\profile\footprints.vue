<template>
	<view class="star-chart-container">
		<view class="back-button" @click="goHome">
			<text class="back-icon">‹</text>
		</view>
		<view class="header">
			<text class="title">我的衡阳印记</text>
			<text class="progress-text">已点亮 {{ checkedInCount }} / {{ totalSpots }} 颗星辰</text>
			<progress :percent="progressPercent" stroke-width="4" activeColor="rgba(255, 215, 0, 0.8)" backgroundColor="rgba(255, 255, 255, 0.1)" class="progress-bar"></progress>
		</view>

		<view class="star-map">
			<view 
				v-for="item in allItems" 
				:key="item.key" 
				class="grid-item" 
				:class="[item.type, { 'unlocked': item.isCheckedIn }]"
				@click="goToDetail(item)"
			>
				<image :src="item.image" class="item-image" mode="aspectFill"></image>
				<view class="item-overlay"></view>
				<view v-if="!item.isCheckedIn" class="lock-icon">
					<text>{{ item.type === 'scenic' ? '未点亮' : '未品尝' }}</text>
				</view>
				<text class="item-name">{{ item.name }}</text>
			</view>
		</view>

		<view class="action-button-container">
			<button class="portal-button" @click="goToQuiz">
				<text>开启科考</text>
			</button>
		</view>
	</view>
</template>

<script>
import { SCENIC_IMAGES, FOOD_IMAGES } from '@/common/config.js';
import { database } from '@/common/database.js';

export default {
	data() {
		return {
			allItems: [],
			footprints: {},
		};
	},
	computed: {
		checkedInCount() {
			return this.allItems.filter(s => s.isCheckedIn).length;
		},
		totalSpots() {
			return this.allItems.length;
		},
		progressPercent() {
			return this.totalSpots > 0 ? (this.checkedInCount / this.totalSpots) * 100 : 0;
		}
	},
	onShow() {
		this.loadData();
	},
	methods: {
		loadData() {
			const scenicSpots = Object.values(database.scenic).map(spot => ({
				...spot,
				type: 'scenic',
				key: `scenic-${spot.id}`,
				image: SCENIC_IMAGES[spot.folder].index
			}));

			const foodItems = Object.values(database.food).map(food => ({
				...food,
				type: 'food',
				key: `food-${food.id}`,
				image: FOOD_IMAGES[food.id]
			}));
			
			const allItems = [...scenicSpots, ...foodItems];

			this.footprints = uni.getStorageSync('checkIns') || {}; // 使用统一的 checkIns

			this.allItems = allItems.map(item => {
				const footprint = this.footprints[item.key];
				const isCheckedIn = !!footprint;
				
				return {
					...item,
					isCheckedIn,
				};
			});
		},
		goToDetail(item) {
			let url = '';
			if (item.type === 'scenic') {
				url = `/pages/scenic/detail?id=${item.id}`;
			} else if (item.type === 'food') {
				url = `/pages/food/detail?id=${item.id}`;
			}

			if (url) {
				uni.navigateTo({ url });
			}
		},
		goHome() {
			uni.reLaunch({
				url: '/pages/index/index'
			});
		},
		goToQuiz() {
			uni.navigateTo({
				url: '/pages/quiz/index'
			});
		}
	}
};
</script>

<style lang="scss">
.star-chart-container {
	background-color: #0c0a18;
	background-image: radial-gradient(circle at 20% 20%, rgba(100, 100, 150, 0.3) 0%, transparent 40%),
	                  radial-gradient(circle at 80% 70%, rgba(150, 100, 100, 0.2) 0%, transparent 30%);
	min-height: 100vh;
	color: #e0e0e0;
	padding: 0 40rpx 40rpx 40rpx;
	box-sizing: border-box;
	padding-top: var(--status-bar-height);

	.back-button {
		position: absolute;
		top: calc(var(--status-bar-height) + 20rpx);
		left: 30rpx;
		width: 70rpx;
		height: 70rpx;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.1);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;
		transition: all 0.3s ease;

		.back-icon {
			font-size: 50rpx;
			color: #a0a0c0;
			font-weight: bold;
			line-height: 70rpx; // for vertical center alignment
		}

		&:active {
			background: rgba(255, 215, 0, 0.2);
			box-shadow: 0 0 15rpx rgba(255, 215, 0, 0.5);
		}
	}

	.header {
		padding-top: 20rpx;
		margin-bottom: 60rpx;
		text-align: center;
	}

	.title {
		font-size: 52rpx;
		font-weight: bold;
		color: #FFD700;
		text-shadow: 0 0 10rpx #FFD700;
		display: block;
		margin-bottom: 10rpx;
		font-family: 'KaiTi', 'STKaiti', serif;
	}

	.progress-text {
		font-size: 28rpx;
		color: #a0a0c0;
		margin-bottom: 30rpx;
	}
	
	.progress-bar {
		border-radius: 4rpx;
		overflow: hidden;
	}

	.star-map {
		display: grid;
		grid-template-columns: repeat(2, 1fr); /* 改为两列以适应不同形状 */
		gap: 40rpx;
	}

	.grid-item {
		position: relative;
		height: 280rpx;
		transition: all 0.4s ease-in-out;
		background-color: #333;
		box-shadow: 0 8rpx 20rpx rgba(0,0,0,0.5);

		&.scenic {
			border-radius: 50%;
		}
		&.food {
			border-radius: 16rpx;
		}
		
		.item-image {
			width: 100%;
			height: 100%;
			filter: grayscale(100%) brightness(0.4);
			transition: all 0.5s ease;
		}

		&.scenic .item-image { border-radius: 50%; }
		&.food .item-image { border-radius: 16rpx; }
		
		.item-overlay {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: radial-gradient(circle, transparent 50%, rgba(0,0,0,0.8) 100%);
		}
		
		&.scenic .item-overlay { border-radius: 50%; }
		&.food .item-overlay { border-radius: 16rpx; }

		.item-name {
			position: absolute;
			bottom: 20rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 100%;
			text-align: center;
			font-size: 28rpx;
			color: #bbb;
			font-weight: bold;
			transition: color 0.5s ease;
		}

		.lock-icon {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			font-size: 24rpx;
			color: rgba(255, 215, 0, 0.4);
			background-color: rgba(0,0,0,0.5);
			padding: 10rpx 20rpx;
			border-radius: 20rpx;
			text-shadow: 0 0 5rpx rgba(0,0,0,0.5);
			transition: all 0.5s ease;
		}

		&.unlocked {
			.item-image {
				filter: grayscale(0%) brightness(1);
			}
			&.scenic .item-image {
				box-shadow: 0 0 15rpx 5rpx rgba(255, 215, 0, 0.4),
				            0 0 30rpx 10rpx rgba(255, 215, 0, 0.2);
			}
			&.food .item-image {
				box-shadow: 0 0 15rpx 5rpx rgba(100, 255, 100, 0.4);
			}
			.item-name {
				color: #FFD700;
			}
			.lock-icon {
				opacity: 0;
				transform: translate(-50%, -50%) scale(0);
			}
		}

		&:active {
			transform: scale(0.95);
		}
	}
}

.action-button-container {
	margin-top: 80rpx;
	text-align: center;
}

.portal-button {
	display: inline-block;
	background: radial-gradient(ellipse at center, rgba(255, 215, 0, 0.8) 0%, rgba(255, 215, 0, 0) 70%);
	color: #fff;
	font-weight: bold;
	padding: 25rpx 70rpx;
	border-radius: 50rpx;
	box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.5);
	border: 1rpx solid rgba(255, 215, 0, 0.3);
	transition: all 0.3s ease;
	
	&:active {
		transform: scale(0.98);
		box-shadow: 0 0 30rpx rgba(255, 215, 0, 0.8);
	}
}
</style> 