"use strict";
const common_vendor = require("../../common/vendor.js");
const common_config = require("../../common/config.js");
const common_database = require("../../common/database.js");
const _sfc_main = {
  data() {
    return {
      allItems: [],
      footprints: {}
    };
  },
  computed: {
    checkedInCount() {
      return this.allItems.filter((s) => s.isCheckedIn).length;
    },
    totalSpots() {
      return this.allItems.length;
    },
    progressPercent() {
      return this.totalSpots > 0 ? this.checkedInCount / this.totalSpots * 100 : 0;
    }
  },
  onShow() {
    this.loadData();
  },
  methods: {
    loadData() {
      const scenicSpots = Object.values(common_database.database.scenic).map((spot) => ({
        ...spot,
        type: "scenic",
        key: `scenic-${spot.id}`,
        image: common_config.SCENIC_IMAGES[spot.folder].index
      }));
      const foodItems = Object.values(common_database.database.food).map((food) => ({
        ...food,
        type: "food",
        key: `food-${food.id}`,
        image: common_config.FOOD_IMAGES[food.id]
      }));
      const allItems = [...scenicSpots, ...foodItems];
      this.footprints = common_vendor.index.getStorageSync("checkIns") || {};
      this.allItems = allItems.map((item) => {
        const footprint = this.footprints[item.key];
        const isCheckedIn = !!footprint;
        return {
          ...item,
          isCheckedIn
        };
      });
    },
    goToDetail(item) {
      let url = "";
      if (item.type === "scenic") {
        url = `/pages/scenic/detail?id=${item.id}`;
      } else if (item.type === "food") {
        url = `/pages/food/detail?id=${item.id}`;
      }
      if (url) {
        common_vendor.index.navigateTo({ url });
      }
    },
    goHome() {
      common_vendor.index.reLaunch({
        url: "/pages/index/index"
      });
    },
    goToQuiz() {
      common_vendor.index.navigateTo({
        url: "/pages/quiz/index"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goHome && $options.goHome(...args)),
    b: common_vendor.t($options.checkedInCount),
    c: common_vendor.t($options.totalSpots),
    d: $options.progressPercent,
    e: common_vendor.f($data.allItems, (item, k0, i0) => {
      return common_vendor.e({
        a: item.image,
        b: !item.isCheckedIn
      }, !item.isCheckedIn ? {
        c: common_vendor.t(item.type === "scenic" ? "未点亮" : "未品尝")
      } : {}, {
        d: common_vendor.t(item.name),
        e: item.key,
        f: common_vendor.n(item.type),
        g: common_vendor.n({
          "unlocked": item.isCheckedIn
        }),
        h: common_vendor.o(($event) => $options.goToDetail(item), item.key)
      });
    }),
    f: common_vendor.o((...args) => $options.goToQuiz && $options.goToQuiz(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/footprints.js.map
