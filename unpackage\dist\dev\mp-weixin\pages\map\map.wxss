/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.map-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
}
.map-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(139, 69, 19, 0.9);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.map-navbar .navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: calc(20rpx + var(--status-bar-height, 0));
}
.map-navbar .navbar-content .back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}
.map-navbar .navbar-content .back-btn .back-icon {
  font-size: 32rpx;
  color: #FFF8DC;
  font-weight: bold;
}
.map-navbar .navbar-content .navbar-title {
  font-size: 36rpx;
  color: #FFD700;
  font-weight: bold;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5);
}
.map-navbar .navbar-content .placeholder {
  width: 60rpx;
}
.map-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}
.map-wrapper .map-view {
  width: 100%;
  height: 100%;
}
.map-wrapper .map-legend {
  position: absolute;
  top: 120rpx;
  right: 20rpx;
  background: rgba(255, 248, 220, 0.95);
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(139, 69, 19, 0.25);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(139, 69, 19, 0.2);
  z-index: 100;
  max-width: 180rpx;
}
.map-wrapper .map-legend .legend-title {
  font-size: 28rpx;
  color: #8B4513;
  font-weight: 700;
  text-align: center;
  margin-bottom: 20rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}
.map-wrapper .map-legend .legend-items {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.map-wrapper .map-legend .legend-items .legend-item {
  display: flex;
  align-items: center;
  padding: 12rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}
.map-wrapper .map-legend .legend-items .legend-item:hover {
  background: rgba(139, 69, 19, 0.1);
  transform: translateX(4rpx);
}
.map-wrapper .map-legend .legend-items .legend-item.active {
  background: rgba(139, 69, 19, 0.2);
  border: 2rpx solid #D2691E;
  transform: scale(1.05);
}
.map-wrapper .map-legend .legend-items .legend-item .legend-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  margin-right: 12rpx;
  border: 2rpx solid rgba(139, 69, 19, 0.3);
  flex-shrink: 0;
}
.map-wrapper .map-legend .legend-items .legend-item .legend-name {
  font-size: 22rpx;
  color: #8B4513;
  font-weight: 600;
  line-height: 1.2;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
  flex: 1;
}
.info-card {
  position: fixed;
  bottom: -400rpx;
  left: 30rpx;
  right: 30rpx;
  background: rgba(255, 248, 220, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(139, 69, 19, 0.3);
  z-index: 1001;
  transition: bottom 0.3s ease-in-out;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.info-card.show {
  bottom: 30rpx;
}
.info-card .card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 2rpx solid rgba(139, 69, 19, 0.1);
}
.info-card .card-header .scenic-name {
  font-size: 36rpx;
  color: #8B4513;
  font-weight: bold;
}
.info-card .card-header .close-btn {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(139, 69, 19, 0.1);
  border-radius: 50%;
}
.info-card .card-header .close-btn .close-icon {
  font-size: 32rpx;
  color: #8B4513;
  font-weight: bold;
}
.info-card .card-content {
  padding: 30rpx;
}
.info-card .card-content .scenic-image {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}
.info-card .card-content .scenic-desc {
  font-size: 28rpx;
  color: #654321;
  line-height: 1.6;
}
.info-card .card-actions {
  display: flex;
  gap: 20rpx;
  padding: 0 30rpx 30rpx;
}
.info-card .card-actions .action-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
}
.info-card .card-actions .action-btn.primary {
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
}
.info-card .card-actions .action-btn.primary .btn-text {
  color: #FFF8DC;
}
.info-card .card-actions .action-btn.secondary {
  background: rgba(139, 69, 19, 0.1);
  border: 2rpx solid #8B4513;
}
.info-card .card-actions .action-btn.secondary .btn-text {
  color: #8B4513;
}
.info-card .card-actions .action-btn .btn-text {
  font-size: 28rpx;
  font-weight: bold;
}