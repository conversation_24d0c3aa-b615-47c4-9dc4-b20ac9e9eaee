"use strict";
const common_vendor = require("../../common/vendor.js");
const common_config = require("../../common/config.js");
const _sfc_main = {
  data() {
    return {
      status: 1,
      times: null,
      weather: {},
      // 天气数据
      scenicImages: common_config.SCENIC_IMAGES,
      // 引入配置
      scenicSpots: [
        { id: 1, name: "南岳衡山", folder: "hengshan" },
        { id: 2, name: "石鼓书院", folder: "shigu" },
        { id: 3, name: "回雁峰", folder: "huiyan" },
        { id: 4, name: "南华大学", folder: "nanhua" },
        { id: 5, name: "东洲岛", folder: "dongzhou" },
        { id: 6, name: "岣嵝峰", folder: "goulou" }
      ],
      recommendations: [
        { id: 1, icon: "🌟", title: "今日推荐", subtitle: "南岳衡山 - 五岳独秀", targetId: 1 },
        { id: 2, icon: "📚", title: "文化之旅", subtitle: "石鼓书院 - 千年学府", targetId: 2 },
        { id: 3, icon: "🐦", title: "登高望远", subtitle: "回雁峰 - 雁城之巅", targetId: 3 },
        { id: 5, icon: "🍃", title: "江心绿洲", subtitle: "东洲岛 - 城市氧吧", targetId: 5 }
      ]
    };
  },
  onLoad() {
    this.startCarousel();
    this.getWeather();
  },
  onUnload() {
    this.stopCarousel();
  },
  methods: {
    // 开始轮播
    startCarousel() {
      this.times = setInterval(() => {
        this.setWindowsImage();
      }, 3e3);
    },
    // 停止轮播
    stopCarousel() {
      if (this.times) {
        clearInterval(this.times);
        this.times = null;
      }
    },
    // 切换轮播图
    setWindowsImage() {
      if (this.status == 6) {
        this.status = 1;
      } else {
        this.status++;
      }
    },
    // 跳转到景点详情（点击轮播图）
    goToScenicDetail() {
      const currentScenic = this.scenicSpots[this.status - 1];
      common_vendor.index.__f__("log", "at pages/index/index.vue:130", "跳转到景点地图:", currentScenic);
      common_vendor.index.navigateTo({
        url: `/pages/map/map?scenicId=${currentScenic.id}`
      });
    },
    // 跳转到推荐景点
    goToRecommendation(item) {
      common_vendor.index.__f__("log", "at pages/index/index.vue:138", "跳转到推荐景点:", item.subtitle);
      common_vendor.index.navigateTo({
        url: `/pages/discover/index?id=${item.targetId}`
      });
    },
    // 跳转到足迹页面
    goToFootprints() {
      common_vendor.index.navigateTo({
        url: "/pages/profile/footprints"
      });
    },
    // 处理提示框点击事件（预留扩展功能）
    handleTipsClick() {
      common_vendor.index.__f__("log", "at pages/index/index.vue:153", "提示框被点击 - 预留功能扩展");
      common_vendor.index.showToast({
        title: "功能开发中...",
        icon: "none"
      });
    },
    // 处理立即探索按钮点击事件
    handleExploreClick() {
      common_vendor.index.__f__("log", "at pages/index/index.vue:163", "立即探索按钮被点击，跳转到美食页面");
      common_vendor.index.navigateTo({
        url: "/pages/food/index"
      });
    },
    // 获取天气信息
    getWeather() {
      common_vendor.index.request({
        url: "https://v.api.aa1.cn/api/api-tianqi-3/index.php?msg=衡阳&type=1",
        method: "GET",
        success: (res) => {
          if (res.statusCode === 200 && res.data && res.data.code === "1") {
            const today = res.data.data[0];
            this.weather = {
              city: "衡阳",
              temperature: today.wendu.split("～")[1],
              // 取最高温
              condition: today.tianqi,
              pm: today.pm,
              icon: this.getWeatherIcon(today.tianqi)
            };
          } else {
            this.handleWeatherError();
          }
        },
        fail: (err) => {
          this.handleWeatherError();
        }
      });
    },
    getWeatherIcon(condition) {
      if (condition.includes("晴"))
        return "☀️";
      if (condition.includes("多云"))
        return "☁️";
      if (condition.includes("阴"))
        return "🌥️";
      if (condition.includes("雨"))
        return "🌧️";
      if (condition.includes("雪"))
        return "❄️";
      return "-";
    },
    handleWeatherError() {
      common_vendor.index.__f__("error", "at pages/index/index.vue:205", "天气API请求失败");
      this.weather = { city: "天气", temperature: "N/A", condition: "获取失败", icon: "❓" };
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.scenicImages.bg,
    b: common_vendor.f($data.scenicSpots, (spot, k0, i0) => {
      return {
        a: spot.id,
        b: $data.status == spot.id ? 1 : 0,
        c: 7 - spot.id,
        d: $data.scenicImages[spot.folder].index
      };
    }),
    c: common_vendor.o(($event) => $options.goToScenicDetail()),
    d: $data.weather.city
  }, $data.weather.city ? {
    e: common_vendor.t($data.weather.icon),
    f: common_vendor.t($data.weather.temperature),
    g: common_vendor.t($data.weather.city),
    h: common_vendor.t($data.weather.condition),
    i: common_vendor.o((...args) => $options.getWeather && $options.getWeather(...args))
  } : {}, {
    j: common_vendor.f($data.recommendations, (item, k0, i0) => {
      return {
        a: common_vendor.t(item.icon),
        b: common_vendor.t(item.title),
        c: common_vendor.t(item.subtitle),
        d: item.id,
        e: common_vendor.o(($event) => $options.goToRecommendation(item), item.id)
      };
    }),
    k: common_vendor.o(($event) => $options.goToFootprints()),
    l: common_vendor.o(($event) => $options.handleExploreClick())
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
