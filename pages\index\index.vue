<template>
	<view class="home-view">
		<swiper class="swiper" vertical>
			<swiper-item class="swiper-item">
				<view class="item-box">
					<!-- 背景图片 -->
					<image class="bg-image" :src="scenicImages.bg" mode="aspectFill"></image>
					<!-- 背景遮罩层 -->
					<view class="bg-overlay"></view>
					<view class="swiper-windows" @click="goToScenicDetail()">
						<!-- 衡阳六大景点轮播图 -->
						<image 
							v-for="spot in scenicSpots"
							:key="spot.id"
							class="windows-image" 
							:style="{ 
								opacity: status == spot.id ? 1 : 0,
								zIndex: 7 - spot.id 
							}"
							:src="scenicImages[spot.folder].index">
						</image>
						<!-- 窗口遮罩和背景 -->
						<view class="windows-mask"></view>
						<view class="windows-bg"></view>
					</view>
				</view>
				<!-- 天气徽章 -->
				<view class="weather-badge" @click="getWeather" v-if="weather.city">
					<view class="weather-icon">{{ weather.icon }}</view>
					<view class="weather-info">
						<text class="temperature">{{ weather.temperature }}°C</text>
						<text class="city-name">{{ weather.city }} · {{ weather.condition }}</text>
					</view>
				</view>
				<!-- 标题图片 -->
				<view class="title-image">
					<text class="title-text">衡阳古韵</text>
					<text class="subtitle-text">探寻千年文化之美</text>
				</view>
				<!-- 提示框 -->
				<swiper class="recommend-swiper" :indicator-dots="false" autoplay="true" interval="4000" duration="1000" circular="true">
					<swiper-item v-for="item in recommendations" :key="item.id" @click="goToRecommendation(item)">
						<view class="recommend-item">
							<text class="recommend-icon">{{ item.icon }}</text>
							<view class="recommend-text-content">
								<text class="recommend-title">{{ item.title }}</text>
								<text class="recommend-subtitle">{{ item.subtitle }}</text>
							</view>
						</view>
					</swiper-item>
				</swiper>
				<!-- 按钮 -->
				<view class="button-group">
					<view class="footprints-btn" @click="goToFootprints()">
						<text class="btn-icon">印</text>
					</view>
					<view class="explore-btn primary" @click="handleExploreClick()">
						<text class="btn-text">🗺️ 衡阳风味</text>
					</view>
				</view>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
	import { SCENIC_IMAGES } from '@/common/config.js';

	export default {
		data() {
			return {
				status: 1,
				times: null,
				weather: {}, // 天气数据
				scenicImages: SCENIC_IMAGES, // 引入配置
				scenicSpots: [
					{ id: 1, name: '南岳衡山', folder: 'hengshan' },
					{ id: 2, name: '石鼓书院', folder: 'shigu' },
					{ id: 3, name: '回雁峰', folder: 'huiyan' },
					{ id: 4, name: '南华大学', folder: 'nanhua' },
					{ id: 5, name: '东洲岛', folder: 'dongzhou' },
					{ id: 6, name: '岣嵝峰', folder: 'goulou' }
				],
				recommendations: [
					{ id: 1, icon: '🌟', title: '今日推荐', subtitle: '南岳衡山 - 五岳独秀', targetId: 1 },
					{ id: 2, icon: '📚', title: '文化之旅', subtitle: '石鼓书院 - 千年学府', targetId: 2 },
					{ id: 3, icon: '🐦', title: '登高望远', subtitle: '回雁峰 - 雁城之巅', targetId: 3 },
					{ id: 5, icon: '🍃', title: '江心绿洲', subtitle: '东洲岛 - 城市氧吧', targetId: 5 },
				]
			};
		},

		onLoad() {
			this.startCarousel()
			this.getWeather()
		},

		onUnload() {
			this.stopCarousel()
		},

		methods: {
			// 开始轮播
			startCarousel() {
				this.times = setInterval(() => {
					this.setWindowsImage()
				}, 3000)
			},

			// 停止轮播
			stopCarousel() {
				if (this.times) {
					clearInterval(this.times)
					this.times = null
				}
			},

			// 切换轮播图
			setWindowsImage() {
				if (this.status == 6) {
					this.status = 1
				} else {
					this.status++
				}
			},

			// 跳转到景点详情（点击轮播图）
			goToScenicDetail() {
				const currentScenic = this.scenicSpots[this.status - 1]
				console.log('跳转到景点地图:', currentScenic)
				uni.navigateTo({
					url: `/pages/map/map?scenicId=${currentScenic.id}`
				})
			},

			// 跳转到推荐景点
			goToRecommendation(item) {
				console.log('跳转到推荐景点:', item.subtitle)
				uni.navigateTo({
					url: `/pages/discover/index?id=${item.targetId}`
				})
			},

			// 跳转到足迹页面
			goToFootprints() {
				uni.navigateTo({
					url: '/pages/profile/footprints'
				});
			},

			// 处理提示框点击事件（预留扩展功能）
			handleTipsClick() {
				console.log('提示框被点击 - 预留功能扩展')
				// TODO: 在这里添加新功能
				uni.showToast({
					title: '功能开发中...',
					icon: 'none'
				})
			},

			// 处理立即探索按钮点击事件
			handleExploreClick() {
				console.log('立即探索按钮被点击，跳转到美食页面');
				uni.navigateTo({
					url: '/pages/food/index'
				});
			},

			// 获取天气信息
			getWeather() {
				uni.request({
					url: 'https://v.api.aa1.cn/api/api-tianqi-3/index.php?msg=衡阳&type=1',
					method: 'GET',
					success: (res) => {
						if (res.statusCode === 200 && res.data && res.data.code === "1") {
							const today = res.data.data[0];
							this.weather = {
								city: '衡阳',
								temperature: today.wendu.split('～')[1], // 取最高温
								condition: today.tianqi,
								pm: today.pm,
								icon: this.getWeatherIcon(today.tianqi)
							};
						} else {
							this.handleWeatherError();
						}
					},
					fail: (err) => {
						this.handleWeatherError();
					}
				});
			},

			getWeatherIcon(condition) {
				if (condition.includes('晴')) return '☀️';
				if (condition.includes('多云')) return '☁️';
				if (condition.includes('阴')) return '🌥️';
				if (condition.includes('雨')) return '🌧️';
				if (condition.includes('雪')) return '❄️';
				return '-';
			},
			
			handleWeatherError() {
				// API失败时的默认或错误状态
				console.error("天气API请求失败");
				this.weather = { city: '天气', temperature: 'N/A', condition: '获取失败', icon: '❓' };
			},

		}
	}
</script>

<style lang="scss">
	.home-view {
		width: 100vw;
		height: 100vh;
	}

	.swiper {
		width: 100vw;
		height: 100vh;
	}

	.swiper-item {
		position: relative;
		width: 100%;
		height: 100%;

		.item-box {
			position: relative;
			width: 100%;
			height: 100%;
		}

		.bg-image {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 0;
		}

		.bg-overlay {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.3);
			z-index: 1;
		}

		.swiper-windows {
			height: 546rpx;
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			width: 100%;
			z-index: 2;

			.windows-image {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 520rpx;
				height: 520rpx;
				opacity: 0;
				transition: all 3s ease-in-out;
				border-radius: 50%;
				object-fit: cover;
			}

			.windows-bg,
			.windows-mask {
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				width: 546rpx;
				height: 546rpx;
				z-index: 99;
				border-radius: 50%;
			}

			.windows-mask {
				background: rgba(0, 0, 0, 0.1);
				border: 8rpx solid rgba(139, 69, 19, 0.8);
			}

			.windows-bg {
				background: rgba(139, 69, 19, 0.2);
				border: 4rpx solid rgba(139, 69, 19, 0.6);
			}
		}
	}

	.title-image {
		position: absolute;
		bottom: 25%;
		left: 50%;
		transform: translateX(-50%);
		text-align: center;
		z-index: 3;

		.title-text {
			display: block;
			font-size: 48rpx;
			font-weight: bold;
			color: #FFD700;
			text-shadow: 2rpx 2rpx 6rpx rgba(0, 0, 0, 0.8);
			margin-bottom: 10rpx;
		}

		.subtitle-text {
			display: block;
			font-size: 28rpx;
			color: #FFF8DC;
			text-shadow: 1rpx 1rpx 4rpx rgba(0, 0, 0, 0.6);
		}
	}

	.flex-box-space-between {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.recommend-swiper {
		position: absolute;
		bottom: 15%;
		left: 50%;
		transform: translateX(-50%);
		width: 702rpx;
		height: 120rpx; /* 增加空间感 */
		z-index: 3;

		/* vue3.0 transition not work in swiper-item, we use css animation */
		.swiper-item {
			animation: fadeIn 1s ease-in-out;
		}

		@keyframes fadeIn {
			from { opacity: 0.5; }
			to { opacity: 1; }
		}

		.recommend-item {
			display: flex;
			align-items: center;
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.4); /* 暗色背景 */
			border-radius: 16rpx; /* 更圆润的边角 */
			padding: 0 30rpx;
			box-sizing: border-box;
			border: 1rpx solid rgba(255, 215, 0, 0.3); /* 金色辉光边框 */
			backdrop-filter: blur(10px); /* 毛玻璃效果 */
		}

		.recommend-icon {
			font-size: 40rpx;
			margin-right: 20rpx;
		}

		.recommend-text-content {
			display: flex;
			flex-direction: column;
		}

		.recommend-title {
			font-size: 28rpx;
			color: #FFD700; /* 金色标题 */
			font-weight: bold;
		}

		.recommend-subtitle {
			font-size: 22rpx;
			color: #FFF8DC; /* 米白副标题 */
		}
	}

	/* .tips-box {
			position: absolute;
			bottom: 15%;
			left: 50%;
			transform: translateX(-50%);
			width: 702rpx;
			height: 82rpx;
			background: rgba(255, 255, 255, 0.9);
			border-radius: 12rpx;
			padding: 0 30rpx;
			box-sizing: border-box;
			font-size: 26rpx;
			color: #333;
			font-weight: bold;
			box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
			z-index: 3;

			.tips-icon {
				font-size: 32rpx;
				margin-right: 10rpx;
			}

			.tips-text {
				flex: 1;
				margin-left: 20rpx;
			}
		} */

	.button-group {
		position: absolute;
		bottom: 5%;
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		align-items: center; /* 确保按钮垂直居中对齐 */
		gap: 20rpx;
		z-index: 3;
	}

	.explore-btn {
		width: 280rpx; /* 调整宽度以适应双按钮布局 */
		height: 88rpx;
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 6rpx 12rpx rgba(139, 69, 19, 0.3);
		background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);

		.btn-text {
			font-size: 28rpx;
			color: #fff;
			font-weight: bold;
			text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.3);
		}
	}

	.footprints-btn {
		width: 88rpx;
		height: 88rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		background: rgba(255, 255, 255, 0.8);
		box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.2);
		backdrop-filter: blur(5px);
		border: 1rpx solid rgba(255, 215, 0, 0.5);

		.btn-icon {
			font-size: 40rpx;
			color: #8B4513;
			font-weight: bold;
		}
	}

	.weather-badge {
		position: absolute;
		top: 60rpx; /* 根据你的状态栏高度调整 */
		left: 30rpx;
		z-index: 10;
		display: flex;
		align-items: center;
		padding: 10rpx 20rpx;
		background: rgba(0, 0, 0, 0.4);
		border-radius: 30rpx;
		backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 215, 0, 0.3);
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

		.weather-icon {
			font-size: 40rpx;
			margin-right: 15rpx;
		}

		.weather-info {
			display: flex;
			flex-direction: column;
			color: #fff;
		}

		.temperature {
			font-size: 32rpx;
			font-weight: bold;
		}

		.city-name {
			font-size: 22rpx;
			color: #eee;
			opacity: 0.9;
		}
	}
</style>
