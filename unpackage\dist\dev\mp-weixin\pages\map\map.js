"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      // 地图中心点（衡阳市中心）
      mapCenter: {
        longitude: 112.571997,
        latitude: 26.89323
      },
      mapScale: 11,
      showInfoCard: false,
      selectedScenic: null,
      // 衡阳六大景点数据
      scenicSpots: [
        {
          id: 1,
          name: "南岳衡山",
          latitude: 27.254167,
          longitude: 112.655833,
          description: '五岳之一，道教佛教圣地，素有"南岳独秀"之美誉',
          image: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanindex.png",
          folder: "hengshan"
        },
        {
          id: 2,
          name: "石鼓书院",
          latitude: 26.891667,
          longitude: 112.570833,
          description: "中国四大书院之一，千年学府，文化底蕴深厚",
          image: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/shigu/shiguindex.png",
          folder: "shigu"
        },
        {
          id: 3,
          name: "回雁峰",
          latitude: 26.883333,
          longitude: 112.583333,
          description: '衡阳八景之首，"北雁南飞，至此歇翅停回"',
          image: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/huiyan/huiyanindex.jpg",
          folder: "huiyan"
        },
        {
          id: 4,
          name: "南华大学",
          latitude: 26.906667,
          longitude: 112.613333,
          description: "现代教育文化地标，培育英才的知识殿堂",
          image: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/nanhua/nanhuaindex.png",
          folder: "nanhua"
        },
        {
          id: 5,
          name: "东洲岛",
          latitude: 26.888889,
          longitude: 112.575,
          description: "湘江中的文化岛屿，诗意盎然的江心绿洲",
          image: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/dongzhou/dongzhouindex.jpg",
          folder: "dongzhou"
        },
        {
          id: 6,
          name: "岣嵝峰",
          latitude: 26.916667,
          longitude: 112.55,
          description: "传统建筑文化代表，古韵悠长的文化名山",
          image: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/goulou/goullouindex.png",
          folder: "goulou"
        }
      ],
      // 地图标记点
      markers: []
    };
  },
  onLoad(options) {
    if (options.scenicId) {
      this.focusOnScenic(parseInt(options.scenicId));
    }
    this.initMarkers();
  },
  methods: {
    // 初始化地图标记
    initMarkers() {
      this.markers = this.scenicSpots.map((spot) => ({
        id: spot.id,
        latitude: spot.latitude,
        longitude: spot.longitude,
        width: 60,
        height: 60,
        iconPath: this.createCustomMarker(spot),
        callout: {
          content: spot.name,
          color: "#8B4513",
          fontSize: 24,
          borderRadius: 12,
          bgColor: "rgba(255, 248, 220, 0.95)",
          padding: 12,
          display: "ALWAYS",
          textAlign: "center",
          borderWidth: 2,
          borderColor: "rgba(139, 69, 19, 0.3)",
          // 设置callout位置
          anchorY: -10,
          anchorX: 0
        }
      }));
    },
    // 创建自定义标记
    createCustomMarker(spot) {
      const iconMap = {
        1: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanmark.jpg",
        // 南岳衡山
        2: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/shigu/shigumark.jpg",
        // 石鼓书院
        3: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/huiyan/huiyanmark.jpg",
        // 回雁峰
        4: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/nanhua/nanhuamark.jpg",
        // 南华大学
        5: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/dongzhou/dongzhoumark.jpg",
        // 东洲岛
        6: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/goulou/gouloumark.jpg"
        // 岣嵝峰
      };
      return iconMap[spot.id] || "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanmark.jpg";
    },
    // 获取标记图标（保留兼容性）
    getMarkerIcon(scenicId) {
      const iconMap = {
        1: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanmark.jpg",
        // 南岳衡山
        2: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/shigu/shigumark.jpg",
        // 石鼓书院
        3: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/huiyan/huiyanmark.jpg",
        // 回雁峰
        4: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/nanhua/nanhuamark.jpg",
        // 南华大学
        5: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/dongzhou/dongzhoumark.jpg",
        // 东洲岛
        6: "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/goulou/gouloumark.jpg"
        // 岣嵝峰
      };
      return iconMap[scenicId] || "https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanmark.jpg";
    },
    // 标记点击事件
    onMarkerTap(e) {
      const markerId = e.markerId || e.detail.markerId;
      const scenic = this.scenicSpots.find((spot) => spot.id === markerId);
      if (scenic) {
        this.selectedScenic = scenic;
        this.showInfoCard = true;
        this.mapCenter = {
          longitude: scenic.longitude,
          latitude: scenic.latitude
        };
        this.mapScale = 14;
      }
    },
    // 自定义标记点击事件
    onCustomMarkerTap(spot) {
      this.selectedScenic = spot;
      this.showInfoCard = true;
      this.mapCenter = {
        longitude: spot.longitude,
        latitude: spot.latitude
      };
      this.mapScale = 14;
    },
    // 获取标记在屏幕上的位置（简化版本）
    getMarkerPosition(spot) {
      return {
        display: "none"
        // 暂时隐藏，使用原生markers
      };
    },
    // 聚焦到指定景点
    focusOnScenic(scenicId) {
      const scenic = this.scenicSpots.find((spot) => spot.id === scenicId);
      if (scenic) {
        this.mapCenter = {
          longitude: scenic.longitude,
          latitude: scenic.latitude
        };
        this.mapScale = 14;
        this.selectedScenic = scenic;
        this.showInfoCard = true;
      }
    },
    // 关闭信息卡片
    closeInfoCard() {
      this.showInfoCard = false;
      this.selectedScenic = null;
      this.mapCenter = {
        longitude: 112.571997,
        latitude: 26.89323
      };
      this.mapScale = 11;
    },
    // 跳转到景点详情
    goToScenicDetail() {
      if (this.selectedScenic) {
        common_vendor.index.navigateTo({
          url: `/pages/scenic/detail?id=${this.selectedScenic.id}`
        });
      }
    },
    // 开始VR体验
    startVRExperience() {
      if (this.selectedScenic) {
        common_vendor.index.__f__("log", "at pages/map/map.vue:292", "开始VR体验:", this.selectedScenic);
        common_vendor.index.navigateTo({
          url: `/pages/vr/experience?scenicId=${this.selectedScenic.id}&name=${encodeURIComponent(this.selectedScenic.name)}`
        });
      } else {
        common_vendor.index.showToast({
          title: "请先选择一个景点",
          icon: "none"
        });
      }
    },
    // 返回首页
    goBack() {
      common_vendor.index.reLaunch({
        url: "/pages/index/index"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.mapCenter.longitude,
    c: $data.mapCenter.latitude,
    d: $data.mapScale,
    e: $data.markers,
    f: common_vendor.o((...args) => $options.onMarkerTap && $options.onMarkerTap(...args)),
    g: common_vendor.f($data.scenicSpots, (spot, k0, i0) => {
      return {
        a: $options.getMarkerIcon(spot.id),
        b: common_vendor.t(spot.name),
        c: spot.id,
        d: $data.selectedScenic && $data.selectedScenic.id === spot.id ? 1 : "",
        e: common_vendor.o(($event) => $options.focusOnScenic(spot.id), spot.id)
      };
    }),
    h: $data.selectedScenic
  }, $data.selectedScenic ? {
    i: common_vendor.t($data.selectedScenic.name),
    j: common_vendor.o((...args) => $options.closeInfoCard && $options.closeInfoCard(...args)),
    k: $data.selectedScenic.image,
    l: common_vendor.t($data.selectedScenic.description),
    m: common_vendor.o((...args) => $options.goToScenicDetail && $options.goToScenicDetail(...args)),
    n: common_vendor.o((...args) => $options.startVRExperience && $options.startVRExperience(...args)),
    o: $data.showInfoCard ? 1 : ""
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/map/map.js.map
