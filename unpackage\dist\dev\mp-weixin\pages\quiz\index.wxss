/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.quiz-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #2c2828;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAAAAAByaaZbAAAEAElEQVR4AY3UQRIkuQ1D0TxIlUQC+GSW739Ah7tjZu29VtDjf5omEKRqFYpB3PLGi374lm6s3x2mHz4whKszFHGskeJTJMh9mAOLyq16tJV1wsWiRqPhuMcVLM8Zr85Ugip6pGjUKSkrS40OaoaTtW9Rgq9S/so8wvlKV+hoUweVkgLpC5Vp46GsryI9XHmqhDUIdC9atdxuFWMzkXUPI+XZkiih3VnYvh/lP0164FwkOsvq9AjpQb85vUeZtZQjnbSMpa/L4wjtOnNk/Bh9mVef9qCpGcgKSQf2HDpId+U9jR6qmEL+IKm6xKiW5sTMRlA5yDNo9OhopQYKnYvkoY6paKRfEBRjp1effY6X0Luu/tiApKg/TdYY695Fpkf0ec446xhFGkm/ifG3wwq00hGa+fkNqmcuFm7ltM5EvNW+WyUWD76krXGHnDytyy9mOpUNFpcybJGCPTO+x9LAWZ4Wh5mpPwimGLoWIXdLHIb39flqchueMNVRIWBFNZeSCW9JJ2ZSo+I9v1k91mjvzb+AvuJfQOeakcKVuyDOA62uM238qptzaRF2duqmbFqM7p23vQ8S31CLyr1VRQ1ZtNtLbaf+p7HSDc7TTkXoXH8DV1YKBH2q8SmM97qU685DamMXFZyCMK5NIktaHYMy0bwq8WAp2KoPElOgUrGlkpDX8VEClI6e4i8yfnvEorLUdc547Aaoo39Q+jyHFVLM6C5UhDxdO6Jgii6pEwWhh/vXZLniCsLENS72j+H52AWSovn04xzhkYuhGl2ig5cvY/2iePvEgT7wgCpbaiUWRZbTDKuSoVZJVGJUdB4YPsNH0Pobq5KyI6mg43ViKr6M/KyVcQ8AqHRLplnsrq+GkfrQ/Sl+8LimfKoSjQD1sa5wmZx/V2DO3xUeu6h1czKO+ki1aTM9taqNyPTUqzsmj+yW7Y8rKuSgT4uFuz0qKRUK0FHPIz4woFNIhy4xh6VLMbN3oa6QZ2rn8QdJ5M+rm6wl7ZEL/f6QO6gIG0PpaWchrKizkvdPU46D8DjrUiovekfUcwaNoejWuGU1en2ERF5F56t06xV0Pbqi//nMFnRAlT2wicQVJS2DVDzFEcr+yuIWhqVSIM/46khDaWmlfnk8VkDVK8FxowqGc1qgWQ8I7JL8zI6/UgWMSJOvpoZsjvZKRmHPqCj68cZ1jufXAwx19LcHkc8FBO3qOi7MM4DONSBUkfrOHXpWvk1IRpb67AJP1H3+r/PHUniO3oM6Z7F3qsDfWadVEIpY5qMaHxWP+iuyzAdUBxbVEbLe2MoF6ki80iXPW42YZEu0VtnlPa0bJBZrfUYjK/nkOdC9Wkr14Yt/cUIVQqGN+7JoXlrc/wIYADtY3Utr9QAAAABJRU5ErkJggg==");
  position: relative;
}
.quiz-container::before, .quiz-container::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 30rpx;
  background-color: #1a1616;
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.4), transparent, rgba(0, 0, 0, 0.4));
  box-shadow: 0 0 15rpx rgba(0, 0, 0, 0.8);
  z-index: 1;
}
.quiz-container::before {
  left: 0;
}
.quiz-container::after {
  right: 0;
}
.chat-history {
  flex: 1;
  padding: 20rpx 50rpx;
  box-sizing: border-box;
  overflow-y: auto;
}
.message-wrapper {
  display: flex;
  margin-bottom: 40rpx;
  max-width: 85%;
  align-items: flex-start;
}
.message-wrapper.user {
  flex-direction: row-reverse;
  margin-left: auto;
}
.message-wrapper.assistant {
  flex-direction: row;
  margin-right: auto;
}
.message-wrapper .avatar.seal {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
  font-family: "KaiTi", "STKaiti", serif;
  font-size: 40rpx;
  border: 4rpx solid;
  transition: all 0.3s ease;
}
.message-wrapper .avatar.seal.user {
  background-color: #a98e72;
  color: #1a1616;
  border-color: #d4b899;
  margin-left: 20rpx;
  box-shadow: 0 0 10rpx rgba(212, 184, 153, 0.3);
}
.message-wrapper .avatar.seal.assistant {
  background-color: #4a423b;
  color: #d4b899;
  border-color: #6a5f55;
  margin-right: 20rpx;
  box-shadow: 0 0 12rpx rgba(212, 184, 153, 0.5);
}
.message-wrapper .message-content.ink-blot {
  padding: 25rpx 35rpx;
  border-radius: 20rpx 20rpx 20rpx 5rpx;
  font-size: 30rpx;
  line-height: 1.7;
  font-family: "KaiTi", "STKaiti", serif;
  position: relative;
  color: #e0e0e0;
}
.message-wrapper .message-content.ink-blot.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50rpx;
  padding: 25rpx 45rpx;
}
.message-wrapper .message-content.ink-blot.loading .dot-flashing {
  position: relative;
  width: 10rpx;
  height: 10rpx;
  border-radius: 5rpx;
  background-color: #e0e0e0;
  color: #e0e0e0;
  animation: dotFlashing 1s infinite linear alternate;
  animation-delay: 0.5s;
}
.message-wrapper .message-content.ink-blot.loading .dot-flashing::before, .message-wrapper .message-content.ink-blot.loading .dot-flashing::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
}
.message-wrapper .message-content.ink-blot.loading .dot-flashing::before {
  left: -20rpx;
  width: 10rpx;
  height: 10rpx;
  border-radius: 5rpx;
  background-color: #e0e0e0;
  color: #e0e0e0;
  animation: dotFlashing 1s infinite alternate;
  animation-delay: 0s;
}
.message-wrapper .message-content.ink-blot.loading .dot-flashing::after {
  left: 20rpx;
  width: 10rpx;
  height: 10rpx;
  border-radius: 5rpx;
  background-color: #e0e0e0;
  color: #e0e0e0;
  animation: dotFlashing 1s infinite alternate;
  animation-delay: 1s;
}
.message-wrapper.user .message-content.ink-blot {
  background-color: rgba(60, 55, 55, 0.8);
  border-radius: 20rpx 20rpx 5rpx 20rpx;
}
.message-wrapper.assistant .message-content.ink-blot {
  background-color: rgba(30, 26, 26, 0.8);
}
.input-area.inkstone {
  display: flex;
  padding: 20rpx 50rpx;
  background-color: #1a1616;
  border-top: 2rpx solid #4a423b;
  box-shadow: 0 -5rpx 15rpx rgba(0, 0, 0, 0.5);
  align-items: center;
  z-index: 2;
}
.input-field {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: none;
  background-color: #2c2828;
  border-radius: 12rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
  color: #e0e0e0;
  font-family: "KaiTi", "STKaiti", serif;
}
.send-button.seal-stamp {
  width: 120rpx;
  height: 80rpx;
  padding: 0;
  margin: 0;
  background-color: #a98e72;
  color: #1a1616;
  border-radius: 12rpx;
  border: 4rpx solid #d4b899;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-family: "KaiTi", "STKaiti", serif;
  box-shadow: 3rpx 3rpx 5rpx rgba(0, 0, 0, 0.3);
}
.send-button.seal-stamp[disabled] {
  background-color: #555;
  border-color: #777;
  opacity: 0.7;
  color: #999;
}
.send-button.seal-stamp::after {
  border: none;
}
@keyframes dotFlashing {
0% {
    background-color: #e0e0e0;
}
50%, 100% {
    background-color: #666;
}
}