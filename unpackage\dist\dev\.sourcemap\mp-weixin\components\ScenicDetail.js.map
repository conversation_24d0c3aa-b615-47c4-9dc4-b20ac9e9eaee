{"version": 3, "file": "ScenicDetail.js", "sources": ["components/ScenicDetail.vue", "../../../../App/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovVXNlcnMvSkxIdWFuZy9EZXNrdG9wL3l1blZyL2NvbXBvbmVudHMvU2NlbmljRGV0YWlsLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"scenic-detail-container\">\r\n\t\t<!-- 卷轴展开动画区域 -->\r\n\t\t<view class=\"scroll-container\">\r\n\t\t\t\r\n\t\t\t<!-- 第一卷：基本信息 -->\r\n\t\t\t<view class=\"scroll-section intro-section\" :class=\"{ 'section-active': activeSection >= 0 }\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<view class=\"section-number\">壹</view>\r\n\t\t\t\t\t<text class=\"section-title\">古韵初识</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"intro-card\">\r\n\t\t\t\t\t<view class=\"card-ornament top-left\"></view>\r\n\t\t\t\t\t<view class=\"card-ornament top-right\"></view>\r\n\t\t\t\t\t<view class=\"card-ornament bottom-left\"></view>\r\n\t\t\t\t\t<view class=\"card-ornament bottom-right\"></view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"intro-content\">\r\n\t\t\t\t\t\t<text class=\"intro-text\">{{ scenic.introduction }}</text>\r\n\t\t\t\t\t\t<view class=\"intro-tags\">\r\n\t\t\t\t\t\t\t<text v-for=\"tag in scenic.tags\" :key=\"tag\" class=\"tag\">{{ tag }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 第二卷：历史传承 -->\r\n\t\t\t<view class=\"scroll-section history-section\" :class=\"{ 'section-active': activeSection >= 1 }\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<view class=\"section-number\">贰</view>\r\n\t\t\t\t\t<text class=\"section-title\">历史传承</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"timeline-container\">\r\n\t\t\t\t\t<view class=\"timeline-line\"></view>\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tv-for=\"(period, index) in scenic.history\" \r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\tclass=\"timeline-item\"\r\n\t\t\t\t\t\t:class=\"{ 'timeline-active': activeTimelineItem >= index }\"\r\n\t\t\t\t\t\t:style=\"{ '--delay': index * 0.2 + 's' }\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"timeline-dot\"></view>\r\n\t\t\t\t\t\t<view class=\"timeline-content\">\r\n\t\t\t\t\t\t\t<text class=\"timeline-period\">{{ period.era }}</text>\r\n\t\t\t\t\t\t\t<text class=\"timeline-desc\">{{ period.description }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 第三卷：文化内涵 -->\r\n\t\t\t<view class=\"scroll-section culture-section\" :class=\"{ 'section-active': activeSection >= 2 }\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<view class=\"section-number\">叁</view>\r\n\t\t\t\t\t<text class=\"section-title\">文化内涵</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"culture-grid\">\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tv-for=\"(aspect, index) in scenic.culture\" \r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\tclass=\"culture-card\"\r\n\t\t\t\t\t\t:class=\"{ 'card-active': activeCultureCard >= index }\"\r\n\t\t\t\t\t\t@click=\"$emit('show-culture-detail', aspect)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"culture-icon\">{{ aspect.icon }}</view>\r\n\t\t\t\t\t\t<text class=\"culture-name\">{{ aspect.name }}</text>\r\n\t\t\t\t\t\t<text class=\"culture-desc\">{{ aspect.description }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 第四卷：今昔对比 -->\r\n\t\t\t<view class=\"scroll-section compare-section\" :class=\"{ 'section-active': activeSection >= 3 }\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<view class=\"section-number\">肆</view>\r\n\t\t\t\t\t<text class=\"section-title\">古今对话</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"compare-container\">\r\n\t\t\t\t\t<view class=\"compare-slider\" :style=\"{ left: compareSliderPosition + '%' }\">\r\n\t\t\t\t\t\t<view class=\"slider-handle\"></view>\r\n\t\t\t\t\t\t<text class=\"slider-label\">{{ compareSliderPosition < 50 ? '古韵悠长' : '今日风华' }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"compare-images\" @touchmove.stop=\"$emit('compare-slide', $event)\">\r\n\t\t\t\t\t\t<image :src=\"scenic.historicalImage\" class=\"compare-image historical\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t<image \r\n\t\t\t\t\t\t\t:src=\"scenic.viewImage\" \r\n\t\t\t\t\t\t\tclass=\"compare-image modern\" \r\n\t\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t\t:style=\"{ clipPath: `polygon(${compareSliderPosition}% 0%, 100% 0%, 100% 100%, ${compareSliderPosition}% 100%)` }\"\r\n\t\t\t\t\t\t></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 第五卷：游览指南 -->\r\n\t\t\t<view class=\"scroll-section guide-section\" :class=\"{ 'section-active': activeSection >= 4 }\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<view class=\"section-number\">伍</view>\r\n\t\t\t\t\t<text class=\"section-title\">游览指南</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"guide-content\">\r\n\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t<view class=\"guide-icon\">🕐</view>\r\n\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t<text class=\"guide-label\">最佳时节</text>\r\n\t\t\t\t\t\t\t<text class=\"guide-value\">{{ scenic.bestTime }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t<view class=\"guide-icon\">🎫</view>\r\n\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t<text class=\"guide-label\">门票信息</text>\r\n\t\t\t\t\t\t\t<text class=\"guide-value\">{{ scenic.ticketInfo }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t<view class=\"guide-icon\">🚌</view>\r\n\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t<text class=\"guide-label\">交通指南</text>\r\n\t\t\t\t\t\t\t<text class=\"guide-value\">{{ scenic.transportation }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t<view class=\"guide-icon\">⭐</view>\r\n\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t<text class=\"guide-label\">游览建议</text>\r\n\t\t\t\t\t\t\t<text class=\"guide-value\">{{ scenic.tips }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tprops: {\r\n\t\tscenic: {\r\n\t\t\ttype: Object,\r\n\t\t\trequired: true\r\n\t\t},\r\n\t\tactiveSection: Number,\r\n\t\tactiveTimelineItem: Number,\r\n\t\tactiveCultureCard: Number,\r\n\t\tcompareSliderPosition: Number,\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* Copy styles from pages/detail/index.vue */\r\n.scroll-container {\r\n\tpadding: 60rpx 0;\r\n}\r\n.scroll-section {\r\n\tmargin-bottom: 120rpx;\r\n\tpadding: 0 60rpx;\r\n}\r\n/* ... */\r\n</style> ", "import Component from 'D:/Users/<USER>/Desktop/yunVr/components/ScenicDetail.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AAwIA,MAAK,YAAU;AAAA,EACd,OAAO;AAAA,IACN,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IACV;AAAA,IACD,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,EACxB;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClJA,GAAG,gBAAgB,SAAS;"}