{"version": 3, "file": "config.js", "sources": ["common/config.js"], "sourcesContent": ["// 图片资源配置 - 使用云存储避免主包过大\r\nconst IMAGE_BASE_URL = 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/'\r\n\r\n// 景点图片配置\r\nexport const SCENIC_IMAGES = {\r\n\t// 南岳衡山\r\n\thengshan: {\r\n\t\tindex: IMAGE_BASE_URL + 'hengshan/hengshanindex.png',\r\n\t\tmark: IMAGE_BASE_URL + 'hengshan/hengshanmark.jpg', \r\n\t\tview: IMAGE_BASE_URL + 'hengshan/hengshanview.jpg'\r\n\t},\r\n\t// 石鼓书院\r\n\tshigu: {\r\n\t\tindex: IMAGE_BASE_URL + 'shigu/shiguindex.png',\r\n\t\tmark: IMAGE_BASE_URL + 'shigu/shigumark.jpg',\r\n\t\tview: IMAGE_BASE_URL + 'shigu/shiguview.jpg'\r\n\t},\r\n\t// 回雁峰\r\n\thuiyan: {\r\n\t\tindex: IMAGE_BASE_URL + 'huiyan/huiyanindex.jpg',\r\n\t\tmark: IMAGE_BASE_URL + 'huiyan/huiyanmark.jpg',\r\n\t\tview: IMAGE_BASE_URL + 'huiyan/huiyanview.jpg'\r\n\t},\r\n\t// 南华大学\r\n\tnanhua: {\r\n\t\tindex: IMAGE_BASE_URL + 'nanhua/nanhuaindex.png',\r\n\t\tmark: IMAGE_BASE_URL + 'nanhua/nanhuamark.jpg',\r\n\t\tview: IMAGE_BASE_URL + 'nanhua/nanhuaview.jpg'\r\n\t},\r\n\t// 东洲岛\r\n\tdongzhou: {\r\n\t\tindex: IMAGE_BASE_URL + 'dongzhou/dongzhouindex.jpg',\r\n\t\tmark: IMAGE_BASE_URL + 'dongzhou/dongzhoumark.jpg',\r\n\t\tview: IMAGE_BASE_URL + 'dongzhou/dongzhouview.jpg'\r\n\t},\r\n\t// 岣嵝峰\r\n\tgoulou: {\r\n\t\tindex: IMAGE_BASE_URL + 'goulou/goullouindex.png',\r\n\t\tmark: IMAGE_BASE_URL + 'goulou/gouloumark.jpg',\r\n\t\tview: IMAGE_BASE_URL + 'goulou/goulouview.jpg'\r\n\t},\r\n\t// 通用背景\r\n\tbg: IMAGE_BASE_URL + 'bg.jpg'\r\n}\r\n\r\n// 美食图片配置\r\nexport const FOOD_IMAGES = {\r\n\tindex: IMAGE_BASE_URL + '美食/美食推荐/foodindex.jpg',\r\n\txiaogurou: IMAGE_BASE_URL + '美食/青椒削骨肉/xiaogurou.jpg',\r\n\tyufen: IMAGE_BASE_URL + '美食/衡阳鱼粉/yufen.jpg',\r\n\ttutouwan: IMAGE_BASE_URL + '美食/衡阳土头碗/tutouwan.jpg',\r\n\tcuidu: IMAGE_BASE_URL + '美食/衡东脆肚/cuidu.jpg',\r\n\ttuji: IMAGE_BASE_URL + '美食/茶油炒土鸡/tuji.jpg',\r\n\tfuzirou: IMAGE_BASE_URL + '美食/杨桥麸子肉/fuzirou.jpg',\r\n}\r\n\r\n// 图片加载工具函数\r\nexport const getImageUrl = (scenicKey, type) => {\r\n\treturn SCENIC_IMAGES[scenicKey] && SCENIC_IMAGES[scenicKey][type] \r\n\t\t? SCENIC_IMAGES[scenicKey][type]\r\n\t\t: IMAGE_BASE_URL + 'default.jpg'\r\n}\r\n\r\n// 美食图片加载工具函数\r\nexport const getFoodImageUrl = (foodKey) => {\r\n\treturn FOOD_IMAGES[foodKey] || IMAGE_BASE_URL + 'default_food.jpg';\r\n} "], "names": [], "mappings": ";AACA,MAAM,iBAAiB;AAGX,MAAC,gBAAgB;AAAA;AAAA,EAE5B,UAAU;AAAA,IACT,OAAO,iBAAiB;AAAA,IACxB,MAAM,iBAAiB;AAAA,IACvB,MAAM,iBAAiB;AAAA,EACvB;AAAA;AAAA,EAED,OAAO;AAAA,IACN,OAAO,iBAAiB;AAAA,IACxB,MAAM,iBAAiB;AAAA,IACvB,MAAM,iBAAiB;AAAA,EACvB;AAAA;AAAA,EAED,QAAQ;AAAA,IACP,OAAO,iBAAiB;AAAA,IACxB,MAAM,iBAAiB;AAAA,IACvB,MAAM,iBAAiB;AAAA,EACvB;AAAA;AAAA,EAED,QAAQ;AAAA,IACP,OAAO,iBAAiB;AAAA,IACxB,MAAM,iBAAiB;AAAA,IACvB,MAAM,iBAAiB;AAAA,EACvB;AAAA;AAAA,EAED,UAAU;AAAA,IACT,OAAO,iBAAiB;AAAA,IACxB,MAAM,iBAAiB;AAAA,IACvB,MAAM,iBAAiB;AAAA,EACvB;AAAA;AAAA,EAED,QAAQ;AAAA,IACP,OAAO,iBAAiB;AAAA,IACxB,MAAM,iBAAiB;AAAA,IACvB,MAAM,iBAAiB;AAAA,EACvB;AAAA;AAAA,EAED,IAAI,iBAAiB;AACtB;AAGY,MAAC,cAAc;AAAA,EAC1B,OAAO,iBAAiB;AAAA,EACxB,WAAW,iBAAiB;AAAA,EAC5B,OAAO,iBAAiB;AAAA,EACxB,UAAU,iBAAiB;AAAA,EAC3B,OAAO,iBAAiB;AAAA,EACxB,MAAM,iBAAiB;AAAA,EACvB,SAAS,iBAAiB;AAC3B;;;"}