/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.food-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5dc;
}
.header-image-container {
  width: 100%;
  height: 50vh;
  position: relative;
}
.header-image-container .header-image {
  width: 100%;
  height: 100%;
}
.header-image-container .header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.6) 0%, transparent 50%);
}
.header-image-container .back-button {
  position: absolute;
  top: var(--status-bar-height);
  left: 20rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header-image-container .back-button .back-icon {
  font-size: 50rpx;
  color: #fff;
  text-shadow: 0 0 5rpx #000;
}
.header-image-container .food-title-container {
  position: absolute;
  bottom: 40rpx;
  left: 40rpx;
  color: #fff;
}
.header-image-container .food-title-container .food-title {
  font-size: 52rpx;
  font-weight: bold;
  display: block;
  text-shadow: 2rpx 2rpx 4rpx #000;
}
.header-image-container .food-title-container .food-category {
  font-size: 28rpx;
  opacity: 0.9;
}
.content-scroll {
  flex: 1;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}
.info-card {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.info-card .card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 20rpx;
  display: block;
}
.info-card .card-content {
  font-size: 28rpx;
  line-height: 1.7;
  color: #333;
}
.info-card .tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-top: 20rpx;
}
.info-card .tags-container .tag {
  background-color: #f0e6d2;
  color: #8B4513;
  padding: 8rpx 15rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}
.info-card .guide-item {
  display: flex;
  padding: 15rpx 0;
  font-size: 28rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.info-card .guide-item:last-child {
  border-bottom: none;
}
.info-card .guide-item .guide-label {
  color: #666;
  width: 160rpx;
}
.info-card .guide-item .guide-value {
  flex: 1;
  color: #333;
}
.bottom-bar {
  display: flex;
  height: 120rpx;
  padding: 20rpx;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.05);
  gap: 20rpx;
}
.bottom-bar .action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
  background-color: #f0f0f0;
  color: #333;
}
.bottom-bar .action-btn .icon {
  margin-right: 10rpx;
}
.bottom-bar .action-btn.primary {
  background-color: #FFD700;
  color: #333;
}