/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.food-gallery-page {
  height: 100vh;
  width: 100vw;
  background-color: #2c2828;
  overflow: hidden;
  position: relative;
}
.header-gallery {
  width: 100%;
  position: fixed;
  /* 改为固定定位 */
  top: 0;
  left: 0;
  z-index: 10;
  transition: height 0.2s ease-out;
}
.header-gallery .header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.header-gallery .header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, #1a1a1a 0%, rgba(26, 26, 26, 0.6) 50%, transparent 100%);
}
.header-gallery .header-content {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 40rpx;
  box-sizing: border-box;
  text-align: center;
}
.header-gallery .back-button {
  position: absolute;
  top: var(--status-bar-height);
  left: 20rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header-gallery .back-button .back-icon {
  font-size: 50rpx;
  color: #fff;
}
.header-gallery .title-container {
  transition: opacity 0.2s ease-out;
}
.header-gallery .main-title {
  font-size: 52rpx;
  font-weight: bold;
  color: #FFD700;
  display: block;
}
.header-gallery .subtitle {
  font-size: 28rpx;
  color: #e0e0e0;
}
.waterfall-container {
  width: 100%;
  height: 100%;
  /* 占满整个屏幕 */
  padding-top: 250px;
  /* 初始顶部画廊高度 */
  box-sizing: border-box;
}
.waterfall-content {
  display: flex;
  padding: 20rpx;
  padding-bottom: 60rpx;
  /* 增加底部空间 */
  box-sizing: border-box;
}
.waterfall-content .column {
  width: 50%;
  padding: 0 15rpx;
  /* 增加列间距 */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  /* 增加卡片垂直间距 */
}
.waterfall-content .food-card {
  width: 100%;
  border-radius: 20rpx;
  /* 更大的圆角 */
  overflow: hidden;
  background-color: #3a3a3e;
  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.7);
  break-inside: avoid;
  position: relative;
}
.waterfall-content .food-card .food-image {
  width: 100%;
  display: block;
}
.waterfall-content .food-card .card-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 40rpx 20rpx 20rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9), transparent);
}
.waterfall-content .food-card .card-info .food-name {
  font-size: 30rpx;
  color: #fff;
  font-weight: bold;
  text-shadow: 1rpx 1rpx 3rpx rgba(0, 0, 0, 0.5);
}