/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
@keyframes slide-up-fade-in {
from {
    opacity: 0;
    transform: translateY(20rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
@keyframes breath {
from {
    transform: scale(1);
    box-shadow: 0 0 5rpx rgba(0, 0, 0, 0.3);
}
to {
    transform: scale(1.05);
    box-shadow: 0 0 15rpx rgba(255, 215, 0, 0.4);
    /* 暗金辉光 */
}
}
.discover-page {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: relative;
}
.page-bg {
  position: absolute;
  top: -5%;
  left: -5%;
  width: 110%;
  height: 110%;
  filter: blur(40px) brightness(0.7);
  z-index: 1;
  background-size: cover;
  background-position: center;
}
.page-bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 2;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.discover-container {
  position: relative;
  z-index: 3;
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: transparent;
  /* position: relative; */
  /* 已被 z-index 包含 */
}
.back-button {
  position: absolute;
  top: calc(var(--status-bar-height) + 20rpx);
  left: 30rpx;
  z-index: 99;
  width: 70rpx;
  height: 70rpx;
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.arrow {
  width: 20rpx;
  height: 20rpx;
  border-left: 4rpx solid white;
  border-bottom: 4rpx solid white;
  transform: translateX(2rpx) rotate(45deg);
}
.featured-card {
  height: 40vh;
  position: relative;
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  background-size: cover;
  background-position: center;
}
.featured-card .card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, transparent 60%);
  z-index: 2;
}
.featured-card .card-content {
  position: relative;
  z-index: 3;
  padding: 40rpx;
}
.featured-card .card-title {
  font-size: 52rpx;
  font-weight: bold;
  display: block;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);
}
.featured-card .card-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 30rpx;
}
.featured-card .card-buttons {
  display: flex;
  gap: 20rpx;
}
.featured-card .card-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}
.featured-card .card-btn.vr {
  background: #FFD700;
  color: #333;
  border: none;
}
.ai-chat-container {
  height: 60vh;
  display: flex;
  flex-direction: column;
  background-color: #2a2a2e;
  overflow: hidden;
}
.chat-history {
  flex: 1;
  padding: 20rpx;
  box-sizing: border-box;
  overflow-y: auto;
}
.message-wrapper {
  display: flex;
  margin-bottom: 30rpx;
  max-width: 85%;
  animation: slide-up-fade-in 0.5s ease-out forwards;
}
.message-wrapper.user {
  flex-direction: row-reverse;
  margin-left: auto;
}
.message-wrapper .avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
  background: #444;
  color: #fff;
}
.message-wrapper .avatar.user {
  margin-left: 20rpx;
  background-color: #a98e72;
  color: #1a1616;
  border-color: #d4b899;
  box-shadow: 0 0 10rpx rgba(212, 184, 153, 0.3);
}
.message-wrapper .avatar.assistant {
  margin-right: 20rpx;
  background-color: #4a423b;
  /* 更深的暗金背景 */
  color: #FFD700;
  /* 金色字体 */
  border-color: #6a5f55;
  /* 深金色边框 */
  animation: breath 2.5s ease-in-out infinite alternate;
}
.message-wrapper .message-content {
  padding: 20rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  line-height: 1.6;
  background: rgba(255, 255, 255, 0.1);
  color: #e0e0e0;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.message-wrapper .message-content.loading {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.message-wrapper .message-content.loading .dot-flashing {
  position: relative;
  width: 8rpx;
  height: 8rpx;
  border-radius: 5rpx;
  background-color: #888;
  color: #888;
  animation: dotFlashing 1s infinite linear alternate;
  animation-delay: 0.5s;
}
.message-wrapper .message-content.loading .dot-flashing::before, .message-wrapper .message-content.loading .dot-flashing::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
}
.message-wrapper .message-content.loading .dot-flashing::before {
  left: -15rpx;
  width: 8rpx;
  height: 8rpx;
  border-radius: 5rpx;
  background-color: #888;
  color: #888;
  animation: dotFlashing 1s infinite alternate;
  animation-delay: 0s;
}
.message-wrapper .message-content.loading .dot-flashing::after {
  left: 15rpx;
  width: 8rpx;
  height: 8rpx;
  border-radius: 5rpx;
  background-color: #888;
  color: #888;
  animation: dotFlashing 1s infinite alternate;
  animation-delay: 1s;
}
.input-area {
  display: flex;
  padding: 20rpx;
  background-color: #1c1c1e;
  border-top: 1rpx solid #444;
}
.input-field {
  flex: 1;
  height: 80rpx;
  padding: 0 30rpx;
  background-color: rgba(0, 0, 0, 0.2);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 40rpx;
  font-size: 28rpx;
  color: #fff;
  margin-right: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.send-button {
  background-color: #FFD700;
  color: #333;
  border-radius: 40rpx;
  border: none;
}
.send-button[disabled] {
  background-color: rgba(255, 255, 255, 0.1);
  color: #999;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
@keyframes dotFlashing {
0% {
    background-color: #888;
}
50%, 100% {
    background-color: #444;
}
}