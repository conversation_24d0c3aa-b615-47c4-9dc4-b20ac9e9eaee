<view class="food-detail-container"><view class="header-image-container"><image src="{{a}}" class="header-image" mode="aspectFill"></image><view class="header-overlay"></view><view class="back-button" bindtap="{{b}}"><text class="back-icon">‹</text></view><view class="food-title-container"><text class="food-title">{{c}}</text><text class="food-category">{{d}}</text></view></view><scroll-view scroll-y="{{true}}" class="content-scroll"><view class="info-card"><text class="card-title">美味简介</text><text class="card-content">{{e}}</text><view class="tags-container"><text wx:for="{{f}}" wx:for-item="tag" wx:key="b" class="tag">{{tag.a}}</text></view></view><view class="info-card"><text class="card-title">品鉴指南</text><view class="guide-item"><text class="guide-label">推荐时节:</text><text class="guide-value">{{g}}</text></view><view class="guide-item"><text class="guide-label">参考价格:</text><text class="guide-value">{{h}}</text></view><view class="guide-item"><text class="guide-label">哪里能吃:</text><text class="guide-value">{{i}}</text></view><view class="guide-item"><text class="guide-label">品鉴建议:</text><text class="guide-value">{{j}}</text></view></view></scroll-view><view class="bottom-bar"><view class="action-btn" bindtap="{{m}}"><text class="icon">{{k}}</text><text>{{l}}</text></view><view class="action-btn primary" bindtap="{{n}}"><text class="icon">📍</text><text>寻找餐厅</text></view></view></view>