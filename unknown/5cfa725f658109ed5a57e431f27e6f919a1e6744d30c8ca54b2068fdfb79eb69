{"version": 3, "file": "map.js", "sources": ["pages/map/map.vue", "../../../../App/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWFwL21hcC52dWU"], "sourcesContent": ["<template>\n\t<view class=\"map-container\">\n\t\t<!-- 头部导航栏 -->\n\t\t<view class=\"map-navbar\">\n\t\t\t<view class=\"navbar-content\">\n\t\t\t\t<view class=\"back-btn\" @click=\"goBack\">\n\t\t\t\t\t<text class=\"back-icon\">←</text>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"navbar-title\">衡阳古韵地图</text>\n\t\t\t\t<view class=\"placeholder\"></view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\n\t\t\n\t\t<!-- 地图容器 -->\n\t\t<view class=\"map-wrapper\">\n\t\t\t<map\n\t\t\t\tclass=\"map-view\"\n\t\t\t\tid=\"hengyangMap\"\n\t\t\t\t:longitude=\"mapCenter.longitude\"\n\t\t\t\t:latitude=\"mapCenter.latitude\"\n\t\t\t\t:scale=\"mapScale\"\n\t\t\t\tlayer-style=\"1\"\n\t\t\t\t:markers=\"markers\"\n\t\t\t\t@markertap=\"onMarkerTap\"\n\t\t\t\tshow-location\n\t\t\t></map>\n\n\t\t\t<!-- 地图图例 -->\n\t\t\t<view class=\"map-legend\">\n\t\t\t\t<view class=\"legend-title\">衡阳景点导览</view>\n\t\t\t\t<view class=\"legend-items\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tv-for=\"spot in scenicSpots\"\n\t\t\t\t\t\t:key=\"spot.id\"\n\t\t\t\t\t\tclass=\"legend-item\"\n\t\t\t\t\t\t:class=\"{ 'active': selectedScenic && selectedScenic.id === spot.id }\"\n\t\t\t\t\t\t@click=\"focusOnScenic(spot.id)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t:src=\"getMarkerIcon(spot.id)\"\n\t\t\t\t\t\t\tclass=\"legend-icon\"\n\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t></image>\n\t\t\t\t\t\t<text class=\"legend-name\">{{ spot.name }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 景点信息卡片 -->\n\t\t<view class=\"info-card\" v-if=\"selectedScenic\" :class=\"{ 'show': showInfoCard }\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<text class=\"scenic-name\">{{ selectedScenic.name }}</text>\n\t\t\t\t<view class=\"close-btn\" @click=\"closeInfoCard\">\n\t\t\t\t\t<text class=\"close-icon\">×</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"card-content\">\n\t\t\t\t<image :src=\"selectedScenic.image\" mode=\"aspectFill\" class=\"scenic-image\"></image>\n\t\t\t\t<text class=\"scenic-desc\">{{ selectedScenic.description }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"card-actions\">\n\t\t\t\t<view class=\"action-btn primary\" @click=\"goToScenicDetail\">\n\t\t\t\t\t<text class=\"btn-text\">🏛️ 详细介绍</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"action-btn secondary\" @click=\"startVRExperience\">\n\t\t\t\t\t<text class=\"btn-text\">🥽 VR体验</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\t// 地图中心点（衡阳市中心）\n\t\t\tmapCenter: {\n\t\t\t\tlongitude: 112.571997,\n\t\t\t\tlatitude: 26.893230\n\t\t\t},\n\t\t\tmapScale: 11,\n\t\t\tshowInfoCard: false,\n\t\t\tselectedScenic: null,\n\t\t\t\n\t\t\t// 衡阳六大景点数据\n\t\t\tscenicSpots: [\n\t\t\t\t{\n\t\t\t\t\tid: 1,\n\t\t\t\t\tname: '南岳衡山',\n\t\t\t\t\tlatitude: 27.254167,\n\t\t\t\t\tlongitude: 112.655833,\n\t\t\t\t\tdescription: '五岳之一，道教佛教圣地，素有\"南岳独秀\"之美誉',\n\t\t\t\t\timage: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanindex.png',\n\t\t\t\t\tfolder: 'hengshan'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 2,\n\t\t\t\t\tname: '石鼓书院',\n\t\t\t\t\tlatitude: 26.891667,\n\t\t\t\t\tlongitude: 112.570833,\n\t\t\t\t\tdescription: '中国四大书院之一，千年学府，文化底蕴深厚',\n\t\t\t\t\timage: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/shigu/shiguindex.png',\n\t\t\t\t\tfolder: 'shigu'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 3,\n\t\t\t\t\tname: '回雁峰',\n\t\t\t\t\tlatitude: 26.883333,\n\t\t\t\t\tlongitude: 112.583333,\n\t\t\t\t\tdescription: '衡阳八景之首，\"北雁南飞，至此歇翅停回\"',\n\t\t\t\t\timage: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/huiyan/huiyanindex.jpg',\n\t\t\t\t\tfolder: 'huiyan'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 4,\n\t\t\t\t\tname: '南华大学',\n\t\t\t\t\tlatitude: 26.906667,\n\t\t\t\t\tlongitude: 112.613333,\n\t\t\t\t\tdescription: '现代教育文化地标，培育英才的知识殿堂',\n\t\t\t\t\timage: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/nanhua/nanhuaindex.png',\n\t\t\t\t\tfolder: 'nanhua'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 5,\n\t\t\t\t\tname: '东洲岛',\n\t\t\t\t\tlatitude: 26.888889,\n\t\t\t\t\tlongitude: 112.575000,\n\t\t\t\t\tdescription: '湘江中的文化岛屿，诗意盎然的江心绿洲',\n\t\t\t\t\timage: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/dongzhou/dongzhouindex.jpg',\n\t\t\t\t\tfolder: 'dongzhou'\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 6,\n\t\t\t\t\tname: '岣嵝峰',\n\t\t\t\t\tlatitude: 26.916667,\n\t\t\t\t\tlongitude: 112.550000,\n\t\t\t\t\tdescription: '传统建筑文化代表，古韵悠长的文化名山',\n\t\t\t\t\timage: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/goulou/goullouindex.png',\n\t\t\t\t\tfolder: 'goulou'\n\t\t\t\t}\n\t\t\t],\n\t\t\t\n\t\t\t// 地图标记点\n\t\t\tmarkers: []\n\t\t}\n\t},\n\t\n\tonLoad(options) {\n\t\t// 如果从首页传来了景点ID，则定位到该景点\n\t\tif (options.scenicId) {\n\t\t\tthis.focusOnScenic(parseInt(options.scenicId))\n\t\t}\n\t\tthis.initMarkers()\n\t},\n\t\n\tmethods: {\n\t\t// 初始化地图标记\n\t\tinitMarkers() {\n\t\t\tthis.markers = this.scenicSpots.map(spot => ({\n\t\t\t\tid: spot.id,\n\t\t\t\tlatitude: spot.latitude,\n\t\t\t\tlongitude: spot.longitude,\n\t\t\t\twidth: 60,\n\t\t\t\theight: 60,\n\t\t\t\ticonPath: this.createCustomMarker(spot),\n\t\t\t\tcallout: {\n\t\t\t\t\tcontent: spot.name,\n\t\t\t\t\tcolor: '#8B4513',\n\t\t\t\t\tfontSize: 24,\n\t\t\t\t\tborderRadius: 12,\n\t\t\t\t\tbgColor: 'rgba(255, 248, 220, 0.95)',\n\t\t\t\t\tpadding: 12,\n\t\t\t\t\tdisplay: 'ALWAYS',\n\t\t\t\t\ttextAlign: 'center',\n\t\t\t\t\tborderWidth: 2,\n\t\t\t\t\tborderColor: 'rgba(139, 69, 19, 0.3)',\n\t\t\t\t\t// 设置callout位置\n\t\t\t\t\tanchorY: -10,\n\t\t\t\t\tanchorX: 0\n\t\t\t\t}\n\t\t\t}))\n\t\t},\n\n\t\t// 创建自定义标记\n\t\tcreateCustomMarker(spot) {\n\t\t\t// 使用圆形的标记图标，提供更好的视觉效果\n\t\t\tconst iconMap = {\n\t\t\t\t1: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanmark.jpg', // 南岳衡山\n\t\t\t\t2: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/shigu/shigumark.jpg',       // 石鼓书院\n\t\t\t\t3: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/huiyan/huiyanmark.jpg',     // 回雁峰\n\t\t\t\t4: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/nanhua/nanhuamark.jpg',     // 南华大学\n\t\t\t\t5: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/dongzhou/dongzhoumark.jpg', // 东洲岛\n\t\t\t\t6: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/goulou/gouloumark.jpg'      // 岣嵝峰\n\t\t\t}\n\t\t\treturn iconMap[spot.id] || 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanmark.jpg'\n\t\t},\n\n\t\t// 获取标记图标（保留兼容性）\n\t\tgetMarkerIcon(scenicId) {\n\t\t\tconst iconMap = {\n\t\t\t\t1: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanmark.jpg', // 南岳衡山\n\t\t\t\t2: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/shigu/shigumark.jpg',       // 石鼓书院\n\t\t\t\t3: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/huiyan/huiyanmark.jpg',     // 回雁峰\n\t\t\t\t4: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/nanhua/nanhuamark.jpg',     // 南华大学\n\t\t\t\t5: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/dongzhou/dongzhoumark.jpg', // 东洲岛\n\t\t\t\t6: 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/goulou/gouloumark.jpg'      // 岣嵝峰\n\t\t\t}\n\t\t\treturn iconMap[scenicId] || 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/hengshan/hengshanmark.jpg'\n\t\t},\n\t\t\n\t\t// 标记点击事件\n\t\tonMarkerTap(e) {\n\t\t\tconst markerId = e.markerId || e.detail.markerId\n\t\t\tconst scenic = this.scenicSpots.find(spot => spot.id === markerId)\n\t\t\tif (scenic) {\n\t\t\t\tthis.selectedScenic = scenic\n\t\t\t\tthis.showInfoCard = true\n\n\t\t\t\t// 移动地图中心到选中的景点\n\t\t\t\tthis.mapCenter = {\n\t\t\t\t\tlongitude: scenic.longitude,\n\t\t\t\t\tlatitude: scenic.latitude\n\t\t\t\t}\n\t\t\t\tthis.mapScale = 14\n\t\t\t}\n\t\t},\n\n\t\t// 自定义标记点击事件\n\t\tonCustomMarkerTap(spot) {\n\t\t\tthis.selectedScenic = spot\n\t\t\tthis.showInfoCard = true\n\n\t\t\t// 移动地图中心到选中的景点\n\t\t\tthis.mapCenter = {\n\t\t\t\tlongitude: spot.longitude,\n\t\t\t\tlatitude: spot.latitude\n\t\t\t}\n\t\t\tthis.mapScale = 14\n\t\t},\n\n\t\t// 获取标记在屏幕上的位置（简化版本）\n\t\tgetMarkerPosition(spot) {\n\t\t\t// 注意：这是一个简化的实现\n\t\t\t// 实际项目中建议使用地图API提供的坐标转换方法\n\t\t\treturn {\n\t\t\t\tdisplay: 'none' // 暂时隐藏，使用原生markers\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 聚焦到指定景点\n\t\tfocusOnScenic(scenicId) {\n\t\t\tconst scenic = this.scenicSpots.find(spot => spot.id === scenicId)\n\t\t\tif (scenic) {\n\t\t\t\tthis.mapCenter = {\n\t\t\t\t\tlongitude: scenic.longitude,\n\t\t\t\t\tlatitude: scenic.latitude\n\t\t\t\t}\n\t\t\t\tthis.mapScale = 14\n\t\t\t\tthis.selectedScenic = scenic\n\t\t\t\tthis.showInfoCard = true\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 关闭信息卡片\n\t\tcloseInfoCard() {\n\t\t\tthis.showInfoCard = false\n\t\t\tthis.selectedScenic = null\n\t\t\t// 恢复地图全景\n\t\t\tthis.mapCenter = {\n\t\t\t\tlongitude: 112.571997,\n\t\t\t\tlatitude: 26.893230\n\t\t\t}\n\t\t\tthis.mapScale = 11\n\t\t},\n\t\t\n\t\t// 跳转到景点详情\n\t\tgoToScenicDetail() {\n\t\t\tif (this.selectedScenic) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/scenic/detail?id=${this.selectedScenic.id}`\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 开始VR体验\n\t\tstartVRExperience() {\n\t\t\tif (this.selectedScenic) {\n\t\t\t\tconsole.log('开始VR体验:', this.selectedScenic)\n\n\t\t\t\t// 跳转到VR体验页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/vr/experience?scenicId=${this.selectedScenic.id}&name=${encodeURIComponent(this.selectedScenic.name)}`\n\t\t\t\t})\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先选择一个景点',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 返回首页\n\t\tgoBack() {\n\t\t\tuni.reLaunch({\n\t\t\t\turl: '/pages/index/index'\n\t\t\t})\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n.map-container {\n\twidth: 100vw;\n\theight: 100vh;\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.map-navbar {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tz-index: 1000;\n\tbackground: rgba(139, 69, 19, 0.9);\n\tbackdrop-filter: blur(10rpx);\n\t\n\t.navbar-content {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 20rpx 30rpx;\n\t\tpadding-top: calc(20rpx + var(--status-bar-height, 0));\n\t\t\n\t\t.back-btn {\n\t\t\twidth: 60rpx;\n\t\t\theight: 60rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tbackground: rgba(255, 255, 255, 0.2);\n\t\t\tborder-radius: 50%;\n\t\t\t\n\t\t\t.back-icon {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tcolor: #FFF8DC;\n\t\t\t\tfont-weight: bold;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.navbar-title {\n\t\t\tfont-size: 36rpx;\n\t\t\tcolor: #FFD700;\n\t\t\tfont-weight: bold;\n\t\t\ttext-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5);\n\t\t}\n\t\t\n\t\t.placeholder {\n\t\t\twidth: 60rpx;\n\t\t}\n\t}\n}\n\n\n\n.map-wrapper {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tz-index: 2;\n\n\t.map-view {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t// 地图图例\n\t.map-legend {\n\t\tposition: absolute;\n\t\ttop: 120rpx;\n\t\tright: 20rpx;\n\t\tbackground: rgba(255, 248, 220, 0.95);\n\t\tborder-radius: 20rpx;\n\t\tpadding: 20rpx;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(139, 69, 19, 0.25);\n\t\tbackdrop-filter: blur(10rpx);\n\t\tborder: 2rpx solid rgba(139, 69, 19, 0.2);\n\t\tz-index: 100;\n\t\tmax-width: 180rpx;\n\n\t\t.legend-title {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #8B4513;\n\t\t\tfont-weight: 700;\n\t\t\ttext-align: center;\n\t\t\tmargin-bottom: 20rpx;\n\t\t\ttext-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);\n\t\t}\n\n\t\t.legend-items {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\tgap: 12rpx;\n\n\t\t\t.legend-item {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 12rpx;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\ttransition: all 0.3s ease;\n\t\t\t\tcursor: pointer;\n\n\t\t\t\t&:hover {\n\t\t\t\t\tbackground: rgba(139, 69, 19, 0.1);\n\t\t\t\t\ttransform: translateX(4rpx);\n\t\t\t\t}\n\n\t\t\t\t&.active {\n\t\t\t\t\tbackground: rgba(139, 69, 19, 0.2);\n\t\t\t\t\tborder: 2rpx solid #D2691E;\n\t\t\t\t\ttransform: scale(1.05);\n\t\t\t\t}\n\n\t\t\t\t.legend-icon {\n\t\t\t\t\twidth: 32rpx;\n\t\t\t\t\theight: 32rpx;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\tmargin-right: 12rpx;\n\t\t\t\t\tborder: 2rpx solid rgba(139, 69, 19, 0.3);\n\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t}\n\n\t\t\t\t.legend-name {\n\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\tcolor: #8B4513;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tline-height: 1.2;\n\t\t\t\t\ttext-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);\n\t\t\t\t\tflex: 1;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n.info-card {\n\tposition: fixed;\n\tbottom: -400rpx;\n\tleft: 30rpx;\n\tright: 30rpx;\n\tbackground: rgba(255, 248, 220, 0.95);\n\tborder-radius: 20rpx;\n\tbox-shadow: 0 10rpx 30rpx rgba(139, 69, 19, 0.3);\n\tz-index: 1001;\n\ttransition: bottom 0.3s ease-in-out;\n\tbackdrop-filter: blur(10rpx);\n\t\n\t&.show {\n\t\tbottom: 30rpx;\n\t}\n\t\n\t.card-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 30rpx;\n\t\tborder-bottom: 2rpx solid rgba(139, 69, 19, 0.1);\n\t\t\n\t\t.scenic-name {\n\t\t\tfont-size: 36rpx;\n\t\t\tcolor: #8B4513;\n\t\t\tfont-weight: bold;\n\t\t}\n\t\t\n\t\t.close-btn {\n\t\t\twidth: 50rpx;\n\t\t\theight: 50rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tbackground: rgba(139, 69, 19, 0.1);\n\t\t\tborder-radius: 50%;\n\t\t\t\n\t\t\t.close-icon {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tcolor: #8B4513;\n\t\t\t\tfont-weight: bold;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.card-content {\n\t\tpadding: 30rpx;\n\t\t\n\t\t.scenic-image {\n\t\t\twidth: 100%;\n\t\t\theight: 200rpx;\n\t\t\tborder-radius: 12rpx;\n\t\t\tmargin-bottom: 20rpx;\n\t\t}\n\t\t\n\t\t.scenic-desc {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #654321;\n\t\t\tline-height: 1.6;\n\t\t}\n\t}\n\t\n\t.card-actions {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t\tpadding: 0 30rpx 30rpx;\n\t\t\n\t\t.action-btn {\n\t\t\tflex: 1;\n\t\t\theight: 80rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tborder-radius: 40rpx;\n\t\t\t\n\t\t\t&.primary {\n\t\t\t\tbackground: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);\n\t\t\t\t\n\t\t\t\t.btn-text {\n\t\t\t\t\tcolor: #FFF8DC;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t&.secondary {\n\t\t\t\tbackground: rgba(139, 69, 19, 0.1);\n\t\t\t\tborder: 2rpx solid #8B4513;\n\t\t\t\t\n\t\t\t\t.btn-text {\n\t\t\t\t\tcolor: #8B4513;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.btn-text {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import MiniProgramPage from 'D:/Users/<USER>/Desktop/yunVr/pages/map/map.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA4EA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,WAAW;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,MACV;AAAA,MACD,UAAU;AAAA,MACV,cAAc;AAAA,MACd,gBAAgB;AAAA;AAAA,MAGhB,aAAa;AAAA,QACZ;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,UACX,aAAa;AAAA,UACb,OAAO;AAAA,UACP,QAAQ;AAAA,QACR;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,UACX,aAAa;AAAA,UACb,OAAO;AAAA,UACP,QAAQ;AAAA,QACR;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,UACX,aAAa;AAAA,UACb,OAAO;AAAA,UACP,QAAQ;AAAA,QACR;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,UACX,aAAa;AAAA,UACb,OAAO;AAAA,UACP,QAAQ;AAAA,QACR;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,UACX,aAAa;AAAA,UACb,OAAO;AAAA,UACP,QAAQ;AAAA,QACR;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,UACX,aAAa;AAAA,UACb,OAAO;AAAA,UACP,QAAQ;AAAA,QACT;AAAA,MACA;AAAA;AAAA,MAGD,SAAS,CAAC;AAAA,IACX;AAAA,EACA;AAAA,EAED,OAAO,SAAS;AAEf,QAAI,QAAQ,UAAU;AACrB,WAAK,cAAc,SAAS,QAAQ,QAAQ,CAAC;AAAA,IAC9C;AACA,SAAK,YAAY;AAAA,EACjB;AAAA,EAED,SAAS;AAAA;AAAA,IAER,cAAc;AACb,WAAK,UAAU,KAAK,YAAY,IAAI,WAAS;AAAA,QAC5C,IAAI,KAAK;AAAA,QACT,UAAU,KAAK;AAAA,QACf,WAAW,KAAK;AAAA,QAChB,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,UAAU,KAAK,mBAAmB,IAAI;AAAA,QACtC,SAAS;AAAA,UACR,SAAS,KAAK;AAAA,UACd,OAAO;AAAA,UACP,UAAU;AAAA,UACV,cAAc;AAAA,UACd,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,WAAW;AAAA,UACX,aAAa;AAAA,UACb,aAAa;AAAA;AAAA,UAEb,SAAS;AAAA,UACT,SAAS;AAAA,QACV;AAAA,MACD,EAAE;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB,MAAM;AAExB,YAAM,UAAU;AAAA,QACf,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,MACJ;AACA,aAAO,QAAQ,KAAK,EAAE,KAAK;AAAA,IAC3B;AAAA;AAAA,IAGD,cAAc,UAAU;AACvB,YAAM,UAAU;AAAA,QACf,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,MACJ;AACA,aAAO,QAAQ,QAAQ,KAAK;AAAA,IAC5B;AAAA;AAAA,IAGD,YAAY,GAAG;AACd,YAAM,WAAW,EAAE,YAAY,EAAE,OAAO;AACxC,YAAM,SAAS,KAAK,YAAY,KAAK,UAAQ,KAAK,OAAO,QAAQ;AACjE,UAAI,QAAQ;AACX,aAAK,iBAAiB;AACtB,aAAK,eAAe;AAGpB,aAAK,YAAY;AAAA,UAChB,WAAW,OAAO;AAAA,UAClB,UAAU,OAAO;AAAA,QAClB;AACA,aAAK,WAAW;AAAA,MACjB;AAAA,IACA;AAAA;AAAA,IAGD,kBAAkB,MAAM;AACvB,WAAK,iBAAiB;AACtB,WAAK,eAAe;AAGpB,WAAK,YAAY;AAAA,QAChB,WAAW,KAAK;AAAA,QAChB,UAAU,KAAK;AAAA,MAChB;AACA,WAAK,WAAW;AAAA,IAChB;AAAA;AAAA,IAGD,kBAAkB,MAAM;AAGvB,aAAO;AAAA,QACN,SAAS;AAAA;AAAA,MACV;AAAA,IACA;AAAA;AAAA,IAGD,cAAc,UAAU;AACvB,YAAM,SAAS,KAAK,YAAY,KAAK,UAAQ,KAAK,OAAO,QAAQ;AACjE,UAAI,QAAQ;AACX,aAAK,YAAY;AAAA,UAChB,WAAW,OAAO;AAAA,UAClB,UAAU,OAAO;AAAA,QAClB;AACA,aAAK,WAAW;AAChB,aAAK,iBAAiB;AACtB,aAAK,eAAe;AAAA,MACrB;AAAA,IACA;AAAA;AAAA,IAGD,gBAAgB;AACf,WAAK,eAAe;AACpB,WAAK,iBAAiB;AAEtB,WAAK,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,MACX;AACA,WAAK,WAAW;AAAA,IAChB;AAAA;AAAA,IAGD,mBAAmB;AAClB,UAAI,KAAK,gBAAgB;AACxBA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,2BAA2B,KAAK,eAAe,EAAE;AAAA,SACtD;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,oBAAoB;AACnB,UAAI,KAAK,gBAAgB;AACxBA,sBAAA,MAAA,MAAA,OAAA,4BAAY,WAAW,KAAK,cAAc;AAG1CA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,iCAAiC,KAAK,eAAe,EAAE,SAAS,mBAAmB,KAAK,eAAe,IAAI,CAAC;AAAA,SACjH;AAAA,aACK;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,SAAS;AACRA,oBAAAA,MAAI,SAAS;AAAA,QACZ,KAAK;AAAA,OACL;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvTA,GAAG,WAAW,eAAe;"}