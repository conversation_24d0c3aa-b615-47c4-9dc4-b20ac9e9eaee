"use strict";
const common_vendor = require("../../common/vendor.js");
const common_config = require("../../common/config.js");
const _sfc_main = {
  data() {
    return {
      coverImage: common_config.FOOD_IMAGES.index,
      allFood: [],
      column1: [],
      column2: [],
      scrollTop: 0,
      initialHeaderHeight: 250
    };
  },
  computed: {
    headerHeight() {
      const newHeight = this.initialHeaderHeight - this.scrollTop;
      return Math.max(120, newHeight);
    },
    titleOpacity() {
      return Math.max(0, 1 - this.scrollTop / 100);
    }
  },
  onLoad() {
    this.loadFoodData();
    this.distributeFood();
  },
  methods: {
    loadFoodData() {
      this.allFood = [
        { key: "xiaogurou", name: "青椒剐骨肉", image: common_config.FOOD_IMAGES.xiaogurou },
        { key: "yufen", name: "衡阳鱼粉", image: common_config.FOOD_IMAGES.yufen },
        { key: "tutouwan", name: "衡阳土头碗", image: common_config.FOOD_IMAGES.tutouwan },
        { key: "cuidu", name: "衡东脆肚", image: common_config.FOOD_IMAGES.cuidu },
        { key: "tuji", name: "茶油炒土鸡", image: common_config.FOOD_IMAGES.tuji },
        { key: "fuzirou", name: "杨桥麸子肉", image: common_config.FOOD_IMAGES.fuzirou }
      ].sort(() => Math.random() - 0.5);
    },
    distributeFood() {
      this.allFood.forEach((food, index) => {
        if (index % 2 === 0) {
          this.column1.push(food);
        } else {
          this.column2.push(food);
        }
      });
    },
    onScroll(e) {
      this.scrollTop = e.detail.scrollTop;
    },
    showFoodDetail(item) {
      common_vendor.index.showToast({
        title: `正在品尝 ${item.name}`,
        icon: "none"
      });
      common_vendor.index.navigateTo({
        url: `/pages/food/detail?id=${item.key}`
      });
    },
    goBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.coverImage,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: $options.titleOpacity,
    d: $options.headerHeight + "px",
    e: common_vendor.f($data.column1, (item, k0, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.name),
        c: item.key,
        d: common_vendor.o(($event) => $options.showFoodDetail(item), item.key)
      };
    }),
    f: common_vendor.f($data.column2, (item, k0, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.name),
        c: item.key,
        d: common_vendor.o(($event) => $options.showFoodDetail(item), item.key)
      };
    }),
    g: common_vendor.o((...args) => $options.onScroll && $options.onScroll(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/food/index.js.map
