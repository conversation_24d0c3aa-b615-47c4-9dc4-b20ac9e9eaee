{"description": "项目配置文件。", "packOptions": {"ignore": []}, "setting": {"urlCheck": false, "es6": true, "postcss": true, "minified": true, "newFeature": true, "bigPackageSizeSupport": true}, "compileType": "miniprogram", "libVersion": "", "appid": "wx1666d23daf3d41e5", "projectname": "yunVr", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"current": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}}