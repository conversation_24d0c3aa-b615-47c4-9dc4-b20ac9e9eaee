{"version": 3, "file": "detail.js", "sources": ["pages/food/detail.vue", "../../../../App/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZm9vZC9kZXRhaWwudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"food-detail-container\">\r\n\t\t<!-- 头部图片 -->\r\n\t\t<view class=\"header-image-container\">\r\n\t\t\t<image :src=\"food.viewImage\" class=\"header-image\" mode=\"aspectFill\"></image>\r\n\t\t\t<view class=\"header-overlay\"></view>\r\n\t\t\t<view class=\"back-button\" @click=\"goBack\">\r\n\t\t\t\t<text class=\"back-icon\">‹</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"food-title-container\">\r\n\t\t\t\t<text class=\"food-title\">{{ food.name }}</text>\r\n\t\t\t\t<text class=\"food-category\">{{ food.category }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 内容区域 -->\r\n\t\t<scroll-view :scroll-y=\"true\" class=\"content-scroll\">\r\n\t\t\t<!-- 简介卡片 -->\r\n\t\t\t<view class=\"info-card\">\r\n\t\t\t\t<text class=\"card-title\">美味简介</text>\r\n\t\t\t\t<text class=\"card-content\">{{ food.introduction }}</text>\r\n\t\t\t\t<view class=\"tags-container\">\r\n\t\t\t\t\t<text v-for=\"tag in food.tags\" :key=\"tag\" class=\"tag\">{{ tag }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 品鉴指南 -->\r\n\t\t\t<view class=\"info-card\">\r\n\t\t\t\t<text class=\"card-title\">品鉴指南</text>\r\n\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t<text class=\"guide-label\">推荐时节:</text>\r\n\t\t\t\t\t<text class=\"guide-value\">{{ food.bestTime }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t<text class=\"guide-label\">参考价格:</text>\r\n\t\t\t\t\t<text class=\"guide-value\">{{ food.ticketInfo }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t<text class=\"guide-label\">哪里能吃:</text>\r\n\t\t\t\t\t<text class=\"guide-value\">{{ food.transportation }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t<text class=\"guide-label\">品鉴建议:</text>\r\n\t\t\t\t\t<text class=\"guide-value\">{{ food.tips }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\r\n\t\t<!-- 底部操作栏 -->\r\n\t\t<view class=\"bottom-bar\">\r\n\t\t\t<view class=\"action-btn\" @click=\"handleCheckIn\">\r\n\t\t\t\t<text class=\"icon\">{{ hasCheckedIn ? '✅' : '尝' }}</text>\r\n\t\t\t\t<text>{{ hasCheckedIn ? '我吃过啦' : '标记想吃' }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"action-btn primary\" @click=\"findRestaurant\">\r\n\t\t\t\t<text class=\"icon\">📍</text>\r\n\t\t\t\t<text>寻找餐厅</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { FOOD_IMAGES } from '@/common/config.js';\r\nimport { database } from '@/common/database.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tfoodId: null,\r\n\t\t\tfood: {},\r\n\t\t\thasCheckedIn: false,\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\tif (options.id) {\r\n\t\t\tthis.foodId = options.id;\r\n\t\t\tthis.loadFoodData();\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tloadFoodData() {\r\n\t\t\tconst data = database.food[this.foodId];\r\n\t\t\tif (data) {\r\n\t\t\t\tdata.viewImage = FOOD_IMAGES[this.foodId];\r\n\t\t\t\tthis.food = data;\r\n\t\t\t\tthis.checkInStatus();\r\n\t\t\t}\r\n\t\t},\r\n\t\tcheckInStatus() {\r\n\t\t\tconst checkIns = uni.getStorageSync('checkIns') || {};\r\n\t\t\tconst key = `food-${this.foodId}`;\r\n\t\t\tthis.hasCheckedIn = !!checkIns[key];\r\n\t\t},\r\n\t\thandleCheckIn() {\r\n\t\t\tconst checkIns = uni.getStorageSync('checkIns') || {};\r\n\t\t\tconst key = `food-${this.foodId}`;\r\n\t\t\tif (this.hasCheckedIn) {\r\n\t\t\t\tdelete checkIns[key];\r\n\t\t\t\tuni.showToast({ title: '已取消标记', icon: 'none' });\r\n\t\t\t} else {\r\n\t\t\t\tcheckIns[key] = { id: this.foodId, type: 'food', name: this.food.name, checkedInAt: new Date().toISOString() };\r\n\t\t\t\tuni.showToast({ title: '已标记为想吃！', icon: 'success' });\r\n\t\t\t}\r\n\t\t\tuni.setStorageSync('checkIns', checkIns);\r\n\t\t\tthis.hasCheckedIn = !this.hasCheckedIn;\r\n\t\t},\r\n\t\tfindRestaurant() {\r\n\t\t\tuni.showToast({ title: '功能开发中...', icon: 'none' });\r\n\t\t},\r\n\t\tgoBack() {\r\n\t\t\tuni.navigateBack();\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.food-detail-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\theight: 100vh;\r\n\tbackground-color: #f5f5dc;\r\n}\r\n\r\n.header-image-container {\r\n\twidth: 100%;\r\n\theight: 50vh;\r\n\tposition: relative;\r\n\t.header-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\t.header-overlay {\r\n\t\tposition: absolute;\r\n\t\ttop: 0; left: 0; right: 0; bottom: 0;\r\n\t\tbackground: linear-gradient(to top, rgba(0,0,0,0.6) 0%, transparent 50%);\r\n\t}\r\n\t.back-button {\r\n\t\tposition: absolute;\r\n\t\ttop: var(--status-bar-height);\r\n\t\tleft: 20rpx;\r\n\t\twidth: 80rpx; height: 80rpx;\r\n\t\tdisplay: flex; align-items: center; justify-content: center;\r\n\t\t.back-icon { font-size: 50rpx; color: #fff; text-shadow: 0 0 5rpx #000; }\r\n\t}\r\n\t.food-title-container {\r\n\t\tposition: absolute;\r\n\t\tbottom: 40rpx;\r\n\t\tleft: 40rpx;\r\n\t\tcolor: #fff;\r\n\t\t.food-title { font-size: 52rpx; font-weight: bold; display: block; text-shadow: 2rpx 2rpx 4rpx #000; }\r\n\t\t.food-category { font-size: 28rpx; opacity: 0.9; }\r\n\t}\r\n}\r\n\r\n.content-scroll {\r\n\tflex: 1;\r\n\tpadding: 20rpx 30rpx;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.info-card {\r\n\tbackground-color: #fff;\r\n\tpadding: 30rpx;\r\n\tborder-radius: 16rpx;\r\n\tmargin-bottom: 30rpx;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);\r\n\t\r\n\t.card-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #8B4513;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\t\r\n\t.card-content {\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 1.7;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.tags-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tgap: 15rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t\t.tag {\r\n\t\t\tbackground-color: #f0e6d2;\r\n\t\t\tcolor: #8B4513;\r\n\t\t\tpadding: 8rpx 15rpx;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.guide-item {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 15rpx 0;\r\n\t\tfont-size: 28rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\t&:last-child { border-bottom: none; }\r\n\t\t.guide-label { color: #666; width: 160rpx; }\r\n\t\t.guide-value { flex: 1; color: #333; }\r\n\t}\r\n}\r\n\r\n.bottom-bar {\r\n\tdisplay: flex;\r\n\theight: 120rpx;\r\n\tpadding: 20rpx;\r\n\tbox-sizing: border-box;\r\n\tbackground-color: #fff;\r\n\tbox-shadow: 0 -4rpx 10rpx rgba(0,0,0,0.05);\r\n\tgap: 20rpx;\r\n\r\n\t.action-btn {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-radius: 12rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tbackground-color: #f0f0f0;\r\n\t\tcolor: #333;\r\n\t\t.icon { margin-right: 10rpx; }\r\n\r\n\t\t&.primary {\r\n\t\t\tbackground-color: #FFD700;\r\n\t\t\tcolor: #333;\r\n\t\t}\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/Users/<USER>/Desktop/yunVr/pages/food/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["database", "FOOD_IMAGES", "uni"], "mappings": ";;;;AAkEA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,QAAQ;AAAA,MACR,MAAM,CAAE;AAAA,MACR,cAAc;AAAA,IACf;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,QAAI,QAAQ,IAAI;AACf,WAAK,SAAS,QAAQ;AACtB,WAAK,aAAY;AAAA,IAClB;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,eAAe;AACd,YAAM,OAAOA,gBAAQ,SAAC,KAAK,KAAK,MAAM;AACtC,UAAI,MAAM;AACT,aAAK,YAAYC,cAAAA,YAAY,KAAK,MAAM;AACxC,aAAK,OAAO;AACZ,aAAK,cAAa;AAAA,MACnB;AAAA,IACA;AAAA,IACD,gBAAgB;AACf,YAAM,WAAWC,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,YAAM,MAAM,QAAQ,KAAK,MAAM;AAC/B,WAAK,eAAe,CAAC,CAAC,SAAS,GAAG;AAAA,IAClC;AAAA,IACD,gBAAgB;AACf,YAAM,WAAWA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,YAAM,MAAM,QAAQ,KAAK,MAAM;AAC/B,UAAI,KAAK,cAAc;AACtB,eAAO,SAAS,GAAG;AACnBA,sBAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,OAAK,CAAG;AAAA,aACxC;AACN,iBAAS,GAAG,IAAI,EAAE,IAAI,KAAK,QAAQ,MAAM,QAAQ,MAAM,KAAK,KAAK,MAAM,cAAa,oBAAI,KAAM,GAAC,YAAW;AAC1GA,sBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,UAAU,CAAC;AAAA,MACpD;AACAA,oBAAAA,MAAI,eAAe,YAAY,QAAQ;AACvC,WAAK,eAAe,CAAC,KAAK;AAAA,IAC1B;AAAA,IACD,iBAAiB;AAChBA,oBAAG,MAAC,UAAU,EAAE,OAAO,YAAY,MAAM,OAAK,CAAG;AAAA,IACjD;AAAA,IACD,SAAS;AACRA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;ACjHA,GAAG,WAAW,eAAe;"}