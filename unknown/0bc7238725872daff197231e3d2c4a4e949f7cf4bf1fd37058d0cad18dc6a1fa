/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.vr-container {
  width: 100vw;
  height: 100vh;
  background: #000;
  position: relative;
  overflow: hidden;
}
.vr-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.vr-navbar .navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  padding-top: calc(20rpx + var(--status-bar-height, 0));
}
.vr-navbar .navbar-content .back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}
.vr-navbar .navbar-content .back-btn .back-icon {
  font-size: 32rpx;
  color: #FFF;
  font-weight: bold;
}
.vr-navbar .navbar-content .navbar-title {
  font-size: 32rpx;
  color: #FFD700;
  font-weight: bold;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.8);
}
.vr-navbar .navbar-content .placeholder {
  width: 60rpx;
}
.vr-content {
  width: 100%;
  height: 100%;
  position: relative;
}
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.loading-overlay .loading-box {
  text-align: center;
}
.loading-overlay .loading-box .loading-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 40rpx;
  animation: pulse 2s infinite;
}
.loading-overlay .loading-box .loading-text {
  font-size: 36rpx;
  color: #FFD700;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}
.loading-overlay .loading-box .loading-desc {
  font-size: 28rpx;
  color: #CCC;
  display: block;
}
.error-box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 60rpx;
}
.error-box .error-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 40rpx;
}
.error-box .error-title {
  font-size: 36rpx;
  color: #FF6B6B;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
}
.error-box .error-desc {
  font-size: 28rpx;
  color: #CCC;
  display: block;
  margin-bottom: 40rpx;
}
.error-box .retry-btn {
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  display: inline-block;
}
.error-box .retry-btn .btn-text {
  color: #FFF;
  font-size: 28rpx;
  font-weight: bold;
}
.vr-tips {
  position: fixed;
  bottom: 30rpx;
  left: 30rpx;
  right: 30rpx;
  z-index: 998;
}
.vr-tips .tips-content {
  background: rgba(0, 0, 0, 0.7);
  padding: 20rpx 30rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.vr-tips .tips-content .tips-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
}
.vr-tips .tips-content .tips-text {
  font-size: 24rpx;
  color: #FFF;
}
@keyframes pulse {
0%, 100% {
    transform: scale(1);
}
50% {
    transform: scale(1.1);
}
}