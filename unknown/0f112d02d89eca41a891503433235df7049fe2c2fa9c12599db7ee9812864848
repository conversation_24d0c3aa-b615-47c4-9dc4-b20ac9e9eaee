{"version": 3, "file": "index.js", "sources": ["pages/food/index.vue", "../../../../App/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZm9vZC9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"food-gallery-page\">\r\n\t\t<!-- 顶部画廊 -->\r\n\t\t<view class=\"header-gallery\" :style=\"{ height: headerHeight + 'px' }\">\r\n\t\t\t<image :src=\"coverImage\" class=\"header-bg\" mode=\"aspectFill\"></image>\r\n\t\t\t<view class=\"header-overlay\"></view>\r\n\t\t\t<view class=\"header-content\">\r\n\t\t\t\t<view class=\"back-button\" @click=\"goBack\">\r\n\t\t\t\t\t<text class=\"back-icon\">‹</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"title-container\" :style=\"{ opacity: titleOpacity }\">\r\n\t\t\t\t\t<text class=\"main-title\">衡阳风味</text>\r\n\t\t\t\t\t<text class=\"subtitle\">舌尖上的古城记忆</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 美食瀑布流 -->\r\n\t\t<scroll-view :scroll-y=\"true\" class=\"waterfall-container\" @scroll=\"onScroll\">\r\n\t\t\t<view class=\"waterfall-content\">\r\n\t\t\t\t<view class=\"column\">\r\n\t\t\t\t\t<view v-for=\"item in column1\" :key=\"item.key\" class=\"food-card\" @click=\"showFoodDetail(item)\">\r\n\t\t\t\t\t\t<image :src=\"item.image\" mode=\"widthFix\" class=\"food-image\"></image>\r\n\t\t\t\t\t\t<view class=\"card-info\">\r\n\t\t\t\t\t\t\t<text class=\"food-name\">{{ item.name }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"column\">\r\n\t\t\t\t\t<view v-for=\"item in column2\" :key=\"item.key\" class=\"food-card\" @click=\"showFoodDetail(item)\">\r\n\t\t\t\t\t\t<image :src=\"item.image\" mode=\"widthFix\" class=\"food-image\"></image>\r\n\t\t\t\t\t\t<view class=\"card-info\">\r\n\t\t\t\t\t\t\t<text class=\"food-name\">{{ item.name }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { FOOD_IMAGES } from '@/common/config.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tcoverImage: FOOD_IMAGES.index,\r\n\t\t\tallFood: [],\r\n\t\t\tcolumn1: [],\r\n\t\t\tcolumn2: [],\r\n\t\t\tscrollTop: 0,\r\n\t\t\tinitialHeaderHeight: 250,\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n\t\theaderHeight() {\r\n\t\t\tconst newHeight = this.initialHeaderHeight - this.scrollTop;\r\n\t\t\treturn Math.max(120, newHeight); // 增加最小高度，确保标题可见\r\n\t\t},\r\n\t\ttitleOpacity() {\r\n\t\t\treturn Math.max(0, 1 - (this.scrollTop / 100));\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.loadFoodData();\r\n\t\tthis.distributeFood();\r\n\t},\r\n\tmethods: {\r\n\t\tloadFoodData() {\r\n\t\t\tthis.allFood = [\r\n\t\t\t\t{ key: 'xiaogurou', name: '青椒剐骨肉', image: FOOD_IMAGES.xiaogurou },\r\n\t\t\t\t{ key: 'yufen', name: '衡阳鱼粉', image: FOOD_IMAGES.yufen },\r\n\t\t\t\t{ key: 'tutouwan', name: '衡阳土头碗', image: FOOD_IMAGES.tutouwan },\r\n\t\t\t\t{ key: 'cuidu', name: '衡东脆肚', image: FOOD_IMAGES.cuidu },\r\n\t\t\t\t{ key: 'tuji', name: '茶油炒土鸡', image: FOOD_IMAGES.tuji },\r\n\t\t\t\t{ key: 'fuzirou', name: '杨桥麸子肉', image: FOOD_IMAGES.fuzirou },\r\n\t\t\t].sort(() => Math.random() - 0.5); // 随机排序增加趣味性\r\n\t\t},\r\n\t\tdistributeFood() {\r\n\t\t\t// 手动实现瀑布流分列，更可靠\r\n\t\t\tthis.allFood.forEach((food, index) => {\r\n\t\t\t\tif (index % 2 === 0) {\r\n\t\t\t\t\tthis.column1.push(food);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.column2.push(food);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tonScroll(e) {\r\n\t\t\tthis.scrollTop = e.detail.scrollTop;\r\n\t\t},\r\n\t\tshowFoodDetail(item) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: `正在品尝 ${item.name}`,\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t\t// 未来可跳转到美食详情页\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/food/detail?id=${item.key}`\r\n\t\t\t});\r\n\t\t},\r\n\t\tgoBack() {\r\n\t\t\tuni.navigateBack();\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.food-gallery-page {\r\n\theight: 100vh;\r\n\twidth: 100vw;\r\n\tbackground-color: #2c2828;\r\n\toverflow: hidden;\r\n\tposition: relative;\r\n}\r\n\r\n.header-gallery {\r\n\twidth: 100%;\r\n\tposition: fixed; /* 改为固定定位 */\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tz-index: 10;\r\n\ttransition: height 0.2s ease-out;\r\n\r\n\t.header-bg {\r\n\t\tposition: absolute;\r\n\t\ttop: 0; left: 0;\r\n\t\twidth: 100%; height: 100%;\r\n\t\tobject-fit: cover;\r\n\t}\r\n\t.header-overlay {\r\n\t\tposition: absolute;\r\n\t\ttop: 0; left: 0;\r\n\t\twidth: 100%; height: 100%;\r\n\t\tbackground: linear-gradient(to top, rgba(26,26,26,1) 0%, rgba(26,26,26,0.6) 50%, transparent 100%);\r\n\t}\r\n\t.header-content {\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\twidth: 100%;\r\n\t\tpadding: 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\ttext-align: center;\r\n\t}\r\n\t.back-button {\r\n\t\tposition: absolute;\r\n\t\ttop: var(--status-bar-height);\r\n\t\tleft: 20rpx;\r\n\t\twidth: 80rpx; height: 80rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\t.back-icon { font-size: 50rpx; color: #fff; }\r\n\t}\r\n\t.title-container {\r\n\t\ttransition: opacity 0.2s ease-out;\r\n\t}\r\n\t.main-title {\r\n\t\tfont-size: 52rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #FFD700;\r\n\t\tdisplay: block;\r\n\t}\r\n\t.subtitle {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #e0e0e0;\r\n\t}\r\n}\r\n\r\n.waterfall-container {\r\n\twidth: 100%;\r\n\theight: 100%; /* 占满整个屏幕 */\r\n\tpadding-top: 250px; /* 初始顶部画廊高度 */\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.waterfall-content {\r\n\tdisplay: flex;\r\n\tpadding: 20rpx;\r\n\tpadding-bottom: 60rpx; /* 增加底部空间 */\r\n\tbox-sizing: border-box;\r\n\r\n\t.column {\r\n\t\twidth: 50%;\r\n\t\tpadding: 0 15rpx; /* 增加列间距 */\r\n\t\tbox-sizing: border-box;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 30rpx; /* 增加卡片垂直间距 */\r\n\t}\r\n\t\r\n\t.food-card {\r\n\t\twidth: 100%;\r\n\t\tborder-radius: 20rpx; /* 更大的圆角 */\r\n\t\toverflow: hidden;\r\n\t\tbackground-color: #3a3a3e;\r\n\t\tbox-shadow: 0 10rpx 25rpx rgba(0,0,0,0.7);\r\n\t\tbreak-inside: avoid;\r\n\t\tposition: relative;\r\n\t\t\r\n\t\t.food-image {\r\n\t\t\twidth: 100%;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t\t\r\n\t\t.card-info {\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 0; left: 0; right: 0;\r\n\t\t\tpadding: 40rpx 20rpx 20rpx;\r\n\t\t\tbackground: linear-gradient(to top, rgba(0,0,0,0.9), transparent);\r\n\t\t\t\r\n\t\t\t.food-name {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\ttext-shadow: 1rpx 1rpx 3rpx rgba(0,0,0,0.5);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/Users/<USER>/Desktop/yunVr/pages/food/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["FOOD_IMAGES", "uni"], "mappings": ";;;AA4CA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,YAAYA,cAAW,YAAC;AAAA,MACxB,SAAS,CAAE;AAAA,MACX,SAAS,CAAE;AAAA,MACX,SAAS,CAAE;AAAA,MACX,WAAW;AAAA,MACX,qBAAqB;AAAA;EAEtB;AAAA,EACD,UAAU;AAAA,IACT,eAAe;AACd,YAAM,YAAY,KAAK,sBAAsB,KAAK;AAClD,aAAO,KAAK,IAAI,KAAK,SAAS;AAAA,IAC9B;AAAA,IACD,eAAe;AACd,aAAO,KAAK,IAAI,GAAG,IAAK,KAAK,YAAY,GAAI;AAAA,IAC9C;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,aAAY;AACjB,SAAK,eAAc;AAAA,EACnB;AAAA,EACD,SAAS;AAAA,IACR,eAAe;AACd,WAAK,UAAU;AAAA,QACd,EAAE,KAAK,aAAa,MAAM,SAAS,OAAOA,cAAW,YAAC,UAAW;AAAA,QACjE,EAAE,KAAK,SAAS,MAAM,QAAQ,OAAOA,cAAW,YAAC,MAAO;AAAA,QACxD,EAAE,KAAK,YAAY,MAAM,SAAS,OAAOA,cAAW,YAAC,SAAU;AAAA,QAC/D,EAAE,KAAK,SAAS,MAAM,QAAQ,OAAOA,cAAW,YAAC,MAAO;AAAA,QACxD,EAAE,KAAK,QAAQ,MAAM,SAAS,OAAOA,cAAW,YAAC,KAAM;AAAA,QACvD,EAAE,KAAK,WAAW,MAAM,SAAS,OAAOA,cAAW,YAAC,QAAS;AAAA,MAC7D,EAAC,KAAK,MAAM,KAAK,OAAS,IAAE,GAAG;AAAA,IAChC;AAAA,IACD,iBAAiB;AAEhB,WAAK,QAAQ,QAAQ,CAAC,MAAM,UAAU;AACrC,YAAI,QAAQ,MAAM,GAAG;AACpB,eAAK,QAAQ,KAAK,IAAI;AAAA,eAChB;AACN,eAAK,QAAQ,KAAK,IAAI;AAAA,QACvB;AAAA,MACD,CAAC;AAAA,IACD;AAAA,IACD,SAAS,GAAG;AACX,WAAK,YAAY,EAAE,OAAO;AAAA,IAC1B;AAAA,IACD,eAAe,MAAM;AACpBC,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,QAAQ,KAAK,IAAI;AAAA,QACxB,MAAM;AAAA,MACP,CAAC;AAEDA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,yBAAyB,KAAK,GAAG;AAAA,MACvC,CAAC;AAAA,IACD;AAAA,IACD,SAAS;AACRA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzGA,GAAG,WAAW,eAAe;"}