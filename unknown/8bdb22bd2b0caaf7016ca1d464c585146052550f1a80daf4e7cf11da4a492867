<template>
	<view class="discover-page" v-if="featuredSpot.image">
		<!-- 全局背景 -->
		<view class="page-bg" :style="{ backgroundImage: `url(${featuredSpot.image})` }"></view>
		<view class="page-bg-overlay"></view>

		<view class="discover-container">
			<!-- 返回按钮 -->
			<view class="back-button" @click="goBack">
				<view class="arrow"></view>
			</view>
			
			<!-- 顶部景点卡片 -->
			<view 
				class="featured-card" 
				:style="{ 
					backgroundImage: `url(${featuredSpot.image})`,
					backgroundPosition: featuredSpot.id === 2 ? 'center 20%' : 'center'
				}"
			>
				<view class="card-overlay"></view>
				<view class="card-content">
					<text class="card-title">{{ featuredSpot.name }}</text>
					<text class="card-subtitle">{{ featuredSpot.subtitle }}</text>
					<view class="card-buttons">
						<button class="card-btn detail" @click="goToDetail">查看详情</button>
						<button class="card-btn vr" @click="goToVR">VR体验</button>
					</view>
				</view>
			</view>

			<!-- AI 对话区域 -->
			<view class="ai-chat-container">
				<scroll-view :scroll-y="true" class="chat-history" :scroll-top="scrollTop">
					<view v-for="(message, index) in chatHistory" :key="index" class="message-wrapper" :class="[message.role]">
						<view class="avatar" :class="[message.role]">{{ message.role === 'assistant' ? '通' : '我' }}</view>
						<view class="message-content">
							<text :user-select="true">{{ message.content }}</text>
						</view>
					</view>
					<view v-if="isLoading" class="message-wrapper assistant">
						<view class="avatar assistant">通</view>
						<view class="message-content loading">
							<view class="dot-flashing"></view>
						</view>
					</view>
				</scroll-view>

				<view class="input-area">
					<input v-model="userInput" type="text" placeholder="聊聊衡阳的风土人情..." class="input-field" :disabled="isLoading" @confirm="sendMessage" />
					<button @click="sendMessage" class="send-button" :disabled="isLoading || !userInput">发送</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import config from '@/api/config.js';
import { SCENIC_IMAGES } from '@/common/config.js';

const SYSTEM_PROMPT_TEMPLATE = (spotName) => `你是一位热情、博学、风趣的衡阳本地导游"衡阳通"。你的任务是：
1.  **角色扮演**: 以"衡阳通"的身份，用自然、欢迎的语气与用户对话。
2.  **开场白**: 你的第一句话必须是："欢迎来到${spotName}！这里是衡阳的瑰宝之一。除了眼前的美景，你还想了解衡阳的哪些风土人情呢？无论是嗦螺的来历，还是王船山的逸闻，我都可以为你娓娓道来。"
3.  **知识范围**: 你是衡阳的万事通。你的知识不限于我给你提供的这个初始景点。你可以自由介绍衡阳的任何方面，包括但不限于：地方美食（如鱼粉、唆螺）、历史名人（如蔡伦、王夫之）、方言文化、未收录的小众景点、民俗节庆等。
4.  **互动风格**: 主动引导对话，激发用户的好奇心。当用户的问题超出你的知识范围时，要礼貌地承认并尝试从其他角度提供相关信息。
5.  **核心要求**: 保持对话的趣味性和互动性，让用户感觉在和一位真实的衡阳朋友聊天。`;

export default {
	data() {
		return {
			featuredSpot: {},
			messages: [],
			userInput: '',
			isLoading: false,
			scrollTop: 0
		};
	},
	computed: {
		chatHistory() {
			return this.messages.filter(m => m.role !== 'system');
		}
	},
	onLoad(options) {
		this.loadFeaturedSpot(options.id);
		this.startChat();
	},
	methods: {
		loadFeaturedSpot(id) {
			const allSpots = {
				1: { id: 1, name: '南岳衡山', subtitle: '五岳独秀，禅宗圣地', folder: 'hengshan' },
				2: { id: 2, name: '石鼓书院', subtitle: '千年学府，理学源头', folder: 'shigu' },
				3: { id: 3, name: '回雁峰', subtitle: '雁城之巅，俯瞰湘江', folder: 'huiyan' },
				4: { id: 4, name: '南华大学', subtitle: '核学府，育人才', folder: 'nanhua' },
				5: { id: 5, name: '东洲岛', subtitle: '江心绿洲，城市氧吧', folder: 'dongzhou' },
				6: { id: 6, name: '岣嵝峰', subtitle: '神秘禹碑，千古之谜', folder: 'goulou' }
			};
			const spotData = allSpots[id] || allSpots[1]; // Fallback to a default
			this.featuredSpot = {
				...spotData,
				image: SCENIC_IMAGES[spotData.folder].view
			};
		},
		startChat() {
			this.messages = [
				{ role: 'system', content: SYSTEM_PROMPT_TEMPLATE(this.featuredSpot.name) },
				{ role: 'assistant', content: `欢迎来到${this.featuredSpot.name}！这里是衡阳的瑰宝之一。除了眼前的美景，你还想了解衡阳的哪些风土人情呢？无论是嗦螺的来历，还是王船山的逸闻，我都可以为你娓娓道来。` }
			];
		},
		sendMessage() {
			if (!this.userInput.trim() || this.isLoading) return;
			const userMessage = { role: 'user', content: this.userInput.trim() };
			this.messages = [...this.messages, userMessage];
			this.userInput = '';
			this.fetchAIResponse();
		},
		async fetchAIResponse() {
			this.isLoading = true;
			this.scrollToBottom();

			// 确保总是包含一个system角色的消息
			const apiMessages = this.messages.filter(msg => msg.role !== 'system');
			apiMessages.unshift({ role: 'system', content: SYSTEM_PROMPT_TEMPLATE(this.featuredSpot.name) });

			try {
				const res = await uni.request({
					url: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
					method: 'POST',
					header: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${config.ZHIPU_API_KEY}`
					},
					data: {
						model: 'glm-4',
						messages: apiMessages,
						stream: false
					}
				});

				if (res.statusCode === 200 && res.data.choices) {
					this.messages.push(res.data.choices[0].message);
				} else {
					this.handleError(res.data);
				}
			} catch (err) {
				this.handleError(err);
			} finally {
				this.isLoading = false;
				this.scrollToBottom();
			}
		},
		handleError(error) {
			console.error('AI请求失败:', error);
			this.messages.push({
				role: 'assistant',
				content: '抱歉，导览线路有些繁忙，请稍后再试。'
			});
		},
		goBack() {
			uni.navigateBack();
		},
		goToDetail() {
			uni.navigateTo({
				url: `/pages/scenic/detail?id=${this.featuredSpot.id}`
			});
		},
		goToVR() {
			uni.navigateTo({
				url: `/pages/vr/experience?scenicId=${this.featuredSpot.id}`
			});
		},
		scrollToBottom() {
			this.$nextTick(() => {
				this.scrollTop = this.messages.length * 1000;
			});
		}
	}
};
</script>

<style lang="scss">
@keyframes slide-up-fade-in {
	from {
		opacity: 0;
		transform: translateY(20rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes breath {
	from {
		transform: scale(1);
		box-shadow: 0 0 5rpx rgba(0,0,0,0.3);
	}
	to {
		transform: scale(1.05);
		box-shadow: 0 0 15rpx rgba(255, 215, 0, 0.4); /* 暗金辉光 */
	}
}

.discover-page {
	height: 100vh;
	width: 100vw;
	overflow: hidden;
	position: relative;
}

.page-bg {
	position: absolute;
	top: -5%;
	left: -5%;
	width: 110%;
	height: 110%;
	filter: blur(40px) brightness(0.7);
	z-index: 1;
	background-size: cover;
	background-position: center;
}

.page-bg-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.2);
	z-index: 2;
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.discover-container {
	position: relative;
	z-index: 3;
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: transparent;
	/* position: relative; */ /* 已被 z-index 包含 */
}

.back-button {
	position: absolute;
	top: calc(var(--status-bar-height) + 20rpx);
	left: 30rpx;
	z-index: 99;
	width: 70rpx;
	height: 70rpx;
	background-color: rgba(0, 0, 0, 0.4);
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	backdrop-filter: blur(5px);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.arrow {
	width: 20rpx;
	height: 20rpx;
	border-left: 4rpx solid white;
	border-bottom: 4rpx solid white;
	transform: translateX(2rpx) rotate(45deg);
}

.featured-card {
	height: 40vh;
	position: relative;
	color: #fff;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	background-size: cover;
	background-position: center;
	
	.card-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, transparent 60%);
		z-index: 2;
	}

	.card-content {
		position: relative;
		z-index: 3;
		padding: 40rpx;
	}

	.card-title {
		font-size: 52rpx;
		font-weight: bold;
		display: block;
		text-shadow: 2rpx 2rpx 4rpx rgba(0,0,0,0.5);
	}

	.card-subtitle {
		font-size: 28rpx;
		opacity: 0.9;
		margin-bottom: 30rpx;
	}

	.card-buttons {
		display: flex;
		gap: 20rpx;
	}

	.card-btn {
		flex: 1;
		background: rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(5px);
		color: #fff;
		border-radius: 40rpx;
		font-size: 28rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.3);

		&.vr {
			background: #FFD700;
			color: #333;
			border: none;
		}
	}
}

.ai-chat-container {
	height: 60vh;
	display: flex;
	flex-direction: column;
	background-color: #2a2a2e;
	overflow: hidden;
}

.chat-history {
	flex: 1;
	padding: 20rpx;
	box-sizing: border-box;
	overflow-y: auto;
}

.message-wrapper {
	display: flex;
	margin-bottom: 30rpx;
	max-width: 85%;
	animation: slide-up-fade-in 0.5s ease-out forwards;
	
	&.user {
		flex-direction: row-reverse;
		margin-left: auto;
	}

	.avatar {
		width: 70rpx;
		height: 70rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: bold;
		flex-shrink: 0;
		background: #444;
		color: #fff;
		
		&.user {
			margin-left: 20rpx;
			background-color: #a98e72;
			color: #1a1616;
			border-color: #d4b899;
			box-shadow: 0 0 10rpx rgba(212, 184, 153, 0.3);
		}
		&.assistant {
			margin-right: 20rpx;
			background-color: #4a423b; /* 更深的暗金背景 */
			color: #FFD700; /* 金色字体 */
			border-color: #6a5f55; /* 深金色边框 */
			animation: breath 2.5s ease-in-out infinite alternate;
		}
	}
	
	.message-content {
		padding: 20rpx;
		border-radius: 24rpx;
		font-size: 28rpx;
		line-height: 1.6;
		background: rgba(255, 255, 255, 0.1);
		color: #e0e0e0;
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10px);
		
		&.loading {
			background: rgba(255, 255, 255, 0.1);
			backdrop-filter: blur(10px);
			.dot-flashing {
				position: relative;
				width: 8rpx;
				height: 8rpx;
				border-radius: 5rpx;
				background-color: #888;
				color: #888;
				animation: dotFlashing 1s infinite linear alternate;
				animation-delay: .5s;
				&::before, &::after { content: ''; display: inline-block; position: absolute; top: 0; }
				&::before { left: -15rpx; width: 8rpx; height: 8rpx; border-radius: 5rpx; background-color: #888; color: #888; animation: dotFlashing 1s infinite alternate; animation-delay: 0s; }
				&::after { left: 15rpx; width: 8rpx; height: 8rpx; border-radius: 5rpx; background-color: #888; color: #888; animation: dotFlashing 1s infinite alternate; animation-delay: 1s; }
			}
		}
	}
}

.input-area {
	display: flex;
	padding: 20rpx;
	background-color: #1c1c1e;
	border-top: 1rpx solid #444;
}

.input-field {
	flex: 1;
	height: 80rpx;
	padding: 0 30rpx;
	background-color: rgba(0, 0, 0, 0.2);
	backdrop-filter: blur(10px);
	border-radius: 40rpx;
	font-size: 28rpx;
	color: #fff;
	margin-right: 20rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.send-button {
	background-color: #FFD700;
	color: #333;
	border-radius: 40rpx;
	border: none;
	&[disabled] { 
		background-color: rgba(255, 255, 255, 0.1);
		color: #999; 
		border: 1rpx solid rgba(255, 255, 255, 0.2);
	}
}

@keyframes dotFlashing {
	0% { background-color: #888; }
	50%, 100% { background-color: #444; }
}
</style> 