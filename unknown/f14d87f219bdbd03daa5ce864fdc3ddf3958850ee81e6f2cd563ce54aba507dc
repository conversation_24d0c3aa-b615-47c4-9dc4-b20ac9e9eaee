{"version": 3, "file": "FoodDetail.js", "sources": ["components/FoodDetail.vue", "../../../../App/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovVXNlcnMvSkxIdWFuZy9EZXNrdG9wL3l1blZyL2NvbXBvbmVudHMvRm9vZERldGFpbC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"food-detail-container\">\r\n\t\t<!-- 卷轴展开动画区域 -->\r\n\t\t<view class=\"scroll-container\">\r\n\t\t\t<!-- 第一卷：食味核心 -->\r\n\t\t\t<view class=\"scroll-section\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<view class=\"section-number\">尝</view>\r\n\t\t\t\t\t<text class=\"section-title\">食味核心</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"intro-card\">\r\n\t\t\t\t\t<view class=\"card-ornament top-left\"></view>\r\n\t\t\t\t\t<view class=\"card-ornament top-right\"></view>\r\n\t\t\t\t\t<view class=\"card-ornament bottom-left\"></view>\r\n\t\t\t\t\t<view class=\"card-ornament bottom-right\"></view>\r\n\t\t\t\t\t<view class=\"intro-content\">\r\n\t\t\t\t\t\t<text class=\"intro-text\">{{ food.introduction }}</text>\r\n\t\t\t\t\t\t<view class=\"intro-tags\">\r\n\t\t\t\t\t\t\t<text v-for=\"tag in food.tags\" :key=\"tag\" class=\"tag\">{{ tag }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 第二卷：美味溯源 -->\r\n\t\t\t<view class=\"scroll-section\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<view class=\"section-number\">源</view>\r\n\t\t\t\t\t<text class=\"section-title\">美味溯源</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"timeline-container\">\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tv-for=\"(period, index) in food.history\" \r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\tclass=\"timeline-item\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"timeline-dot\"></view>\r\n\t\t\t\t\t\t<view class=\"timeline-content\">\r\n\t\t\t\t\t\t\t<text class=\"timeline-period\">{{ period.era }}</text>\r\n\t\t\t\t\t\t\t<text class=\"timeline-desc\">{{ period.description }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 第三卷：风味特色 -->\r\n\t\t\t<view class=\"scroll-section\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<view class=\"section-number\">味</view>\r\n\t\t\t\t\t<text class=\"section-title\">风味特色</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"culture-grid\">\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tv-for=\"(aspect, index) in food.culture\" \r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\tclass=\"culture-card\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"culture-icon\">{{ aspect.icon }}</view>\r\n\t\t\t\t\t\t<text class=\"culture-name\">{{ aspect.name }}</text>\r\n\t\t\t\t\t\t<text class=\"culture-desc\">{{ aspect.description }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 第四卷：品鉴指南 -->\r\n\t\t\t<view class=\"scroll-section\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<view class=\"section-number\">品</view>\r\n\t\t\t\t\t<text class=\"section-title\">品鉴指南</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"guide-content\">\r\n\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t<view class=\"guide-icon\">🕐</view>\r\n\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t<text class=\"guide-label\">推荐时节</text>\r\n\t\t\t\t\t\t\t<text class=\"guide-value\">{{ food.bestTime }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t<view class=\"guide-icon\">💰</view>\r\n\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t<text class=\"guide-label\">参考价格</text>\r\n\t\t\t\t\t\t\t<text class=\"guide-value\">{{ food.ticketInfo }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t<view class=\"guide-icon\">📍</view>\r\n\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t<text class=\"guide-label\">哪里能吃</text>\r\n\t\t\t\t\t\t\t<text class=\"guide-value\">{{ food.transportation }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t<view class=\"guide-icon\">⭐</view>\r\n\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t<text class=\"guide-label\">品鉴建议</text>\r\n\t\t\t\t\t\t\t<text class=\"guide-value\">{{ food.tips }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tprops: {\r\n\t\tfood: {\r\n\t\t\ttype: Object,\r\n\t\t\trequired: true\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 这里可以从 detail/index.vue 复制并简化相关样式\r\n// 为了保持简洁，暂时只提供基础结构\r\n// ... (样式将在下一步重构中填充)\r\n.scroll-container {\r\n\tpadding: 60rpx 0;\r\n}\r\n.scroll-section {\r\n\tmargin-bottom: 120rpx;\r\n\tpadding: 0 60rpx;\r\n}\r\n/* 更多样式细节将在后续步骤中添加 */\r\n</style> ", "import Component from 'D:/Users/<USER>/Desktop/yunVr/components/FoodDetail.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AA0GA,MAAK,YAAU;AAAA,EACd,OAAO;AAAA,IACN,MAAM;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACX;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChHA,GAAG,gBAAgB,SAAS;"}