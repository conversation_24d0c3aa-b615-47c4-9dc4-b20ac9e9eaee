<template>
	<view class="food-detail-container">
		<!-- 头部图片 -->
		<view class="header-image-container">
			<image :src="food.viewImage" class="header-image" mode="aspectFill"></image>
			<view class="header-overlay"></view>
			<view class="back-button" @click="goBack">
				<text class="back-icon">‹</text>
			</view>
			<view class="food-title-container">
				<text class="food-title">{{ food.name }}</text>
				<text class="food-category">{{ food.category }}</text>
			</view>
		</view>

		<!-- 内容区域 -->
		<scroll-view :scroll-y="true" class="content-scroll">
			<!-- 简介卡片 -->
			<view class="info-card">
				<text class="card-title">美味简介</text>
				<text class="card-content">{{ food.introduction }}</text>
				<view class="tags-container">
					<text v-for="tag in food.tags" :key="tag" class="tag">{{ tag }}</text>
				</view>
			</view>

			<!-- 品鉴指南 -->
			<view class="info-card">
				<text class="card-title">品鉴指南</text>
				<view class="guide-item">
					<text class="guide-label">推荐时节:</text>
					<text class="guide-value">{{ food.bestTime }}</text>
				</view>
				<view class="guide-item">
					<text class="guide-label">参考价格:</text>
					<text class="guide-value">{{ food.ticketInfo }}</text>
				</view>
				<view class="guide-item">
					<text class="guide-label">哪里能吃:</text>
					<text class="guide-value">{{ food.transportation }}</text>
				</view>
				<view class="guide-item">
					<text class="guide-label">品鉴建议:</text>
					<text class="guide-value">{{ food.tips }}</text>
				</view>
			</view>
		</scroll-view>

		<!-- 底部操作栏 -->
		<view class="bottom-bar">
			<view class="action-btn" @click="handleCheckIn">
				<text class="icon">{{ hasCheckedIn ? '✅' : '尝' }}</text>
				<text>{{ hasCheckedIn ? '我吃过啦' : '标记想吃' }}</text>
			</view>
			<view class="action-btn primary" @click="findRestaurant">
				<text class="icon">📍</text>
				<text>寻找餐厅</text>
			</view>
		</view>
	</view>
</template>

<script>
import { FOOD_IMAGES } from '@/common/config.js';
import { database } from '@/common/database.js';

export default {
	data() {
		return {
			foodId: null,
			food: {},
			hasCheckedIn: false,
		}
	},
	onLoad(options) {
		if (options.id) {
			this.foodId = options.id;
			this.loadFoodData();
		}
	},
	methods: {
		loadFoodData() {
			const data = database.food[this.foodId];
			if (data) {
				data.viewImage = FOOD_IMAGES[this.foodId];
				this.food = data;
				this.checkInStatus();
			}
		},
		checkInStatus() {
			const checkIns = uni.getStorageSync('checkIns') || {};
			const key = `food-${this.foodId}`;
			this.hasCheckedIn = !!checkIns[key];
		},
		handleCheckIn() {
			const checkIns = uni.getStorageSync('checkIns') || {};
			const key = `food-${this.foodId}`;
			if (this.hasCheckedIn) {
				delete checkIns[key];
				uni.showToast({ title: '已取消标记', icon: 'none' });
			} else {
				checkIns[key] = { id: this.foodId, type: 'food', name: this.food.name, checkedInAt: new Date().toISOString() };
				uni.showToast({ title: '已标记为想吃！', icon: 'success' });
			}
			uni.setStorageSync('checkIns', checkIns);
			this.hasCheckedIn = !this.hasCheckedIn;
		},
		findRestaurant() {
			uni.showToast({ title: '功能开发中...', icon: 'none' });
		},
		goBack() {
			uni.navigateBack();
		}
	}
}
</script>

<style lang="scss">
.food-detail-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5dc;
}

.header-image-container {
	width: 100%;
	height: 50vh;
	position: relative;
	.header-image {
		width: 100%;
		height: 100%;
	}
	.header-overlay {
		position: absolute;
		top: 0; left: 0; right: 0; bottom: 0;
		background: linear-gradient(to top, rgba(0,0,0,0.6) 0%, transparent 50%);
	}
	.back-button {
		position: absolute;
		top: var(--status-bar-height);
		left: 20rpx;
		width: 80rpx; height: 80rpx;
		display: flex; align-items: center; justify-content: center;
		.back-icon { font-size: 50rpx; color: #fff; text-shadow: 0 0 5rpx #000; }
	}
	.food-title-container {
		position: absolute;
		bottom: 40rpx;
		left: 40rpx;
		color: #fff;
		.food-title { font-size: 52rpx; font-weight: bold; display: block; text-shadow: 2rpx 2rpx 4rpx #000; }
		.food-category { font-size: 28rpx; opacity: 0.9; }
	}
}

.content-scroll {
	flex: 1;
	padding: 20rpx 30rpx;
	box-sizing: border-box;
}

.info-card {
	background-color: #fff;
	padding: 30rpx;
	border-radius: 16rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
	
	.card-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #8B4513;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.card-content {
		font-size: 28rpx;
		line-height: 1.7;
		color: #333;
	}

	.tags-container {
		display: flex;
		flex-wrap: wrap;
		gap: 15rpx;
		margin-top: 20rpx;
		.tag {
			background-color: #f0e6d2;
			color: #8B4513;
			padding: 8rpx 15rpx;
			border-radius: 8rpx;
			font-size: 24rpx;
		}
	}

	.guide-item {
		display: flex;
		padding: 15rpx 0;
		font-size: 28rpx;
		border-bottom: 1rpx solid #f0f0f0;
		&:last-child { border-bottom: none; }
		.guide-label { color: #666; width: 160rpx; }
		.guide-value { flex: 1; color: #333; }
	}
}

.bottom-bar {
	display: flex;
	height: 120rpx;
	padding: 20rpx;
	box-sizing: border-box;
	background-color: #fff;
	box-shadow: 0 -4rpx 10rpx rgba(0,0,0,0.05);
	gap: 20rpx;

	.action-btn {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: bold;
		background-color: #f0f0f0;
		color: #333;
		.icon { margin-right: 10rpx; }

		&.primary {
			background-color: #FFD700;
			color: #333;
		}
	}
}
</style> 