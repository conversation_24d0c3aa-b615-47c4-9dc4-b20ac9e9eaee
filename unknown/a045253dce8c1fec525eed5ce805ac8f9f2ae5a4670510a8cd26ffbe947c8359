<view class="home-view"><swiper class="swiper" vertical><swiper-item class="swiper-item"><view class="item-box"><image class="bg-image" src="{{a}}" mode="aspectFill"></image><view class="bg-overlay"></view><view class="swiper-windows" bindtap="{{c}}"><image wx:for="{{b}}" wx:for-item="spot" wx:key="a" class="windows-image" style="{{'opacity:' + spot.b + ';' + ('z-index:' + spot.c)}}" src="{{spot.d}}"></image><view class="windows-mask"></view><view class="windows-bg"></view></view></view><view wx:if="{{d}}" class="weather-badge" bindtap="{{i}}"><view class="weather-icon">{{e}}</view><view class="weather-info"><text class="temperature">{{f}}°C</text><text class="city-name">{{g}} · {{h}}</text></view></view><view class="title-image"><text class="title-text">衡阳古韵</text><text class="subtitle-text">探寻千年文化之美</text></view><swiper class="recommend-swiper" indicator-dots="{{false}}" autoplay="true" interval="4000" duration="1000" circular="true"><swiper-item wx:for="{{j}}" wx:for-item="item" wx:key="d" bindtap="{{item.e}}"><view class="recommend-item"><text class="recommend-icon">{{item.a}}</text><view class="recommend-text-content"><text class="recommend-title">{{item.b}}</text><text class="recommend-subtitle">{{item.c}}</text></view></view></swiper-item></swiper><view class="button-group"><view class="footprints-btn" bindtap="{{k}}"><text class="btn-icon">印</text></view><view class="explore-btn primary" bindtap="{{l}}"><text class="btn-text">🗺️ 衡阳风味</text></view></view></swiper-item></swiper></view>