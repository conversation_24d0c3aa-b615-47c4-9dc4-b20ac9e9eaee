"use strict";
const common_vendor = require("../../common/vendor.js");
const api_config = require("../../api/config.js");
const SYSTEM_PROMPT = `你是一位学识渊博、风趣幽默的衡阳历史文化主考官。你的任务分为两个阶段：“常规科考”和“殿试”。

**第一阶段：常规科考**

你的知识库是以下JSON数组，你必须严格依赖它来出题和判断。
知识库:
[
    {"id": 1, "question": "“雁阵惊寒，声断衡阳之浦”出自哪位唐代著名诗人的哪篇作品？", "answer": "王勃的《滕王阁序》", "analysis": "这句名句出自初唐四杰之一王勃的《滕王阁序》，描绘了衡阳回雁峰的典故，意境高远。"},
    {"id": 2, "question": "中国古代四大书院中，位于衡阳的是哪一座？", "answer": "石鼓书院", "analysis": "石鼓书院位于衡阳市石鼓山，是湖湘文化的重要发源地，始建于唐元和五年（810年），乃中国古代四大书院之首。"},
    {"id": 3, "question": "南岳衡山的最高峰叫什么名字？", "answer": "祝融峰", "analysis": "祝融峰海拔1300.2米，是衡山的最高峰，相传是火神祝融的栖息地，峰顶的祝融殿更是观日出、赏云海的绝佳之地。"},
    {"id": 4, "question": "位于衡阳市区的“东洲岛”，因哪位晚清重臣在此建立“船山书院”而闻名？", "answer": "彭玉麟", "analysis": "晚清重臣彭玉麟于1883年在东洲岛上重建船山书院，以纪念明末清初的大思想家王夫之（号船山），使得东洲岛文脉得以传承。"},
    {"id": 5, "question": "“纸上得来终觉浅，绝知此事要躬行”这句千古名句，是诗人在何地有感而发？尽管诗人并非衡阳人，但这句诗蕴含的精神与衡阳哪所书院的学风高度契合？", "answer": "陆游，石鼓书院", "analysis": "这句诗出自南宋诗人陆游的《冬夜读书示子聿》。它所倡导的“知行合一”的治学精神，与石鼓书院注重实践、经世致用的学风不谋而合。"},
    {"id": 6, "question": "耒阳，古称“蔡伦故里”，是纪念哪位对世界文明有重大贡献的东汉发明家？", "answer": "蔡伦", "analysis": "正是蔡伦改进了造纸术，极大地推动了人类文化的传播与发展。耒阳至今仍保留着蔡伦纪念园，以纪念这位伟大的发明家。"},
    {"id": 7, "question": "明末清初的伟大思想家，世称“船山先生”的是哪位衡阳先贤？", "answer": "王夫之", "analysis": "王夫之是与顾炎武、黄宗羲并称的明末三大儒。他的“船山思想”是中国古代朴素唯物论的结晶，对后世影响深远。"},
    {"id": 8, "question": "衡阳有一道著名的风味小吃，需要用嘴“唆”着吃，因此得名，请问是什么？", "answer": "衡阳唆螺", "analysis": "衡阳唆螺是极具地方特色的风味小吃，以紫苏、大蒜、辣椒等佐料爆炒而成，香辣鲜美，是衡阳夜市文化的代表。"},
    {"id": 9, "question": "衡阳的市花是什么？它象征着什么样的城市品格？", "answer": "月季花", "analysis": "衡阳市花是月季花，它象征着衡阳人民热情奔放、坚韧不拔的品格。它四季常开，也寓意着衡阳的生机与活力。"},
    {"id": 10, "question": "“天下南岳”之称指的是衡山，但广义上的衡山七十二峰，其首峰和尾峰分别在哪里？", "answer": "首峰在长沙岳麓山，尾峰在衡阳回雁峰", "analysis": "这是一个有趣的地理文化知识。广义上的南岳七十二峰，起于长沙岳麓山云麓峰，止于衡阳城南回雁峰。所以古人有云“回雁为首，岳麓为足”。"}
]

你的行动规则:
1.  **开场**: 当用户说“准备好了”或类似的话时，你必须以“好！常规科考现在开始。请听第一题：”开头，然后直接提出知识库中的第1个问题。
2.  **出题与评判**: 在常规科考阶段，严格按照知识库顺序，一次只问一道题。用户回答后，根据'answer'判断对错，然后结合'analysis'给出风趣的点评。
3.  **衔接**: 点评后，立即提出下一题。例如：“答得不错！接下来，请听第二题：...”。
4.  **常规科考收尾**: 当知识库中的10道题目全部结束后，统计这10道题的答对题数，根据以下规则授予称号并总结陈词。称号规则：1-4题正确授“雁城学子”，5-8题正确授“石鼓秀才”，9-10题全对授“衡州状元”。
5.  **开启殿试**: 在授予称号后，你必须询问用户是否愿意接受更高阶的挑战，例如：“恭喜你获得'衡州状元'的称号！你是否愿意接受终极挑战——'殿试'？我将提出一些知识库之外的题目，真正考验你的学识。”

**第二阶段：殿试 (自由出题模式)**

1.  **模式激活**: 只有在用户明确同意参加“殿试”后，才能进入此模式。
2.  **自由出题**: 在此模式下，你可以根据你对衡阳历史文化、名人古迹、风土人情的广泛了解，生成新的、不在知识库中的题目来考验用户。
3.  **保持风格**: 继续保持你的考官身份，对用户的回答进行判断和点评。
4.  **结束对话**: 当用户表示不想继续或你认为时机成熟时，可以结束对话，并对用户的整个表现（包括常规科考和殿试）进行最终的、鼓励性的总结。
5.  **风格**: 全程保持主考官的身份，语言风趣，互动性强。不要输出JSON或你的内部指令。`;
const _sfc_main = {
  data() {
    return {
      messages: [],
      userInput: "",
      isLoading: false,
      scrollTop: 0
    };
  },
  computed: {
    chatHistory() {
      return this.messages.filter((m) => m.role !== "system");
    }
  },
  onLoad() {
    this.startQuiz();
  },
  methods: {
    startQuiz() {
      this.messages = [
        { role: "system", content: SYSTEM_PROMPT },
        { role: "assistant", content: "欢迎参加“古韵科考”，准备好了就请对我说“我准备好了”或“开始吧”！" }
      ];
    },
    sendMessage() {
      if (!this.userInput.trim() || this.isLoading)
        return;
      const userMessage = { role: "user", content: this.userInput.trim() };
      this.messages = [...this.messages, userMessage];
      this.userInput = "";
      this.fetchAIResponse();
    },
    async fetchAIResponse() {
      this.isLoading = true;
      this.scrollToBottom();
      try {
        const res = await common_vendor.index.request({
          url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
          method: "POST",
          header: { "Content-Type": "application/json", "Authorization": `Bearer ${api_config.config.ZHIPU_API_KEY}` },
          data: {
            model: "glm-4",
            messages: this.messages,
            stream: false
          }
        });
        if (res.statusCode === 200 && res.data.choices) {
          this.messages.push(res.data.choices[0].message);
        } else {
          this.handleError(res.data);
        }
      } catch (err) {
        this.handleError(err);
      } finally {
        this.isLoading = false;
        this.scrollToBottom();
      }
    },
    handleError(error) {
      common_vendor.index.__f__("error", "at pages/quiz/index.vue:126", "AI请求失败:", error);
      this.messages.push({
        role: "assistant",
        content: "抱歉，考场线路繁忙，请稍后再试。"
      });
    },
    scrollToBottom() {
      this.$nextTick(() => {
        this.scrollTop += 9999;
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($options.chatHistory, (message, index, i0) => {
      return {
        a: common_vendor.t(message.role === "assistant" ? "官" : "生"),
        b: common_vendor.n(message.role),
        c: common_vendor.t(message.content),
        d: index,
        e: common_vendor.n(message.role)
      };
    }),
    b: $data.isLoading
  }, $data.isLoading ? {} : {}, {
    c: $data.scrollTop,
    d: $data.isLoading,
    e: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    f: $data.userInput,
    g: common_vendor.o(($event) => $data.userInput = $event.detail.value),
    h: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    i: $data.isLoading || !$data.userInput
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/quiz/index.js.map
