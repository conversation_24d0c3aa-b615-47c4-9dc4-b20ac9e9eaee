import operate from './operate.js'
import { wxLogin } from '@/api/user.js'
const http = (options) => {
	// 请求拦截器
	const beforeRequest = (config) => {
	  // 添加 Token
	  const token = uni.getStorageSync('token');
	  if (token) {
	    config.header = {
	      ...config.header,
	      Authorization: `Bearer ${token}`
	    };
	  }
	  return config;
	};
	  
	// 执行请求拦截
	const mergedOptions = beforeRequest({
	  url: operate.api() + options.url,
	  method: options.method || 'GET',
	  data: options.data,
	  header: options.header || {}
	});    
	  
	
	return new Promise((resolve, reject) => {
		uni.request({
			...mergedOptions,
			success: (res) => {
				if(res.data.code!==200){ //自定请求失败的情况
					// 统一处理 HTTP 错误码（如 401、404、500）
					handleError(res);
					reject(res);
				} else {
					resolve(res.data)//成功
				}
			},
			// 这里的接口请求，如果出现问题就输出接口请求失败
			fail: (err) => {
				 // 响应拦截器 - 网络错误处理
				handleNetworkError(error);
				reject(error);
			}
		})
	})
}

// 3. 错误处理函数
const handleError = (response) => {
  switch (response.data.code) {
    case 401:
      uni.login({
      	provider: 'weixin',
      	success: res => {
      		wxLogin(res.code, uni.getAccountInfoSync().miniProgram.appId, '', '').then(res => {
      			uni.setStorageSync('token', res.data.token);
      			uni.setStorageSync('wxUser', res.data.wxUser);
      			uni.setStorageSync('openId', res.data.openId);
      			uni.hideLoading()
      		})
      	},
      	fail: err => {
      		console.log(err)
      	}
      })
      break;
    case 500:
      uni.showToast({ title: '服务器内部错误', icon: 'none' });
      break;
    default:
      uni.showToast({ title: `请求失败: ${response.data.code}`, icon: 'none' });
  }
};

const handleNetworkError = (error) => {
  uni.showToast({ title: '网络连接失败，请检查网络', icon: 'none' });
};
export default http
