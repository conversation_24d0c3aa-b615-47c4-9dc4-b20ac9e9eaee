"use strict";
const common_vendor = require("../../common/vendor.js");
const common_config = require("../../common/config.js");
const common_database = require("../../common/database.js");
const _sfc_main = {
  data() {
    return {
      foodId: null,
      food: {},
      hasCheckedIn: false
    };
  },
  onLoad(options) {
    if (options.id) {
      this.foodId = options.id;
      this.loadFoodData();
    }
  },
  methods: {
    loadFoodData() {
      const data = common_database.database.food[this.foodId];
      if (data) {
        data.viewImage = common_config.FOOD_IMAGES[this.foodId];
        this.food = data;
        this.checkInStatus();
      }
    },
    checkInStatus() {
      const checkIns = common_vendor.index.getStorageSync("checkIns") || {};
      const key = `food-${this.foodId}`;
      this.hasCheckedIn = !!checkIns[key];
    },
    handleCheckIn() {
      const checkIns = common_vendor.index.getStorageSync("checkIns") || {};
      const key = `food-${this.foodId}`;
      if (this.hasCheckedIn) {
        delete checkIns[key];
        common_vendor.index.showToast({ title: "已取消标记", icon: "none" });
      } else {
        checkIns[key] = { id: this.foodId, type: "food", name: this.food.name, checkedInAt: (/* @__PURE__ */ new Date()).toISOString() };
        common_vendor.index.showToast({ title: "已标记为想吃！", icon: "success" });
      }
      common_vendor.index.setStorageSync("checkIns", checkIns);
      this.hasCheckedIn = !this.hasCheckedIn;
    },
    findRestaurant() {
      common_vendor.index.showToast({ title: "功能开发中...", icon: "none" });
    },
    goBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.food.viewImage,
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: common_vendor.t($data.food.name),
    d: common_vendor.t($data.food.category),
    e: common_vendor.t($data.food.introduction),
    f: common_vendor.f($data.food.tags, (tag, k0, i0) => {
      return {
        a: common_vendor.t(tag),
        b: tag
      };
    }),
    g: common_vendor.t($data.food.bestTime),
    h: common_vendor.t($data.food.ticketInfo),
    i: common_vendor.t($data.food.transportation),
    j: common_vendor.t($data.food.tips),
    k: common_vendor.t($data.hasCheckedIn ? "✅" : "尝"),
    l: common_vendor.t($data.hasCheckedIn ? "我吃过啦" : "标记想吃"),
    m: common_vendor.o((...args) => $options.handleCheckIn && $options.handleCheckIn(...args)),
    n: common_vendor.o((...args) => $options.findRestaurant && $options.findRestaurant(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/food/detail.js.map
