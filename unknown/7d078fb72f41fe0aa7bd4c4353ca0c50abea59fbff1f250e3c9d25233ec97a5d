{"version": 3, "file": "detail.js", "sources": ["pages/scenic/detail.vue", "../../../../App/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc2NlbmljL2RldGFpbC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"detail-container\" @touchstart=\"onTouchStart\" @touchend=\"onTouchEnd\">\r\n\t\t<view class=\"back-button\" @click=\"goBack\">\r\n\t\t\t<text class=\"back-icon\">‹</text>\r\n\t\t</view>\r\n\t\t<!-- 沉浸式头部 -->\r\n\t\t<view class=\"immersive-header\" :style=\"{ transform: `translateY(${headerOffset}px)` }\">\r\n\t\t\t<image \r\n\t\t\t\t:src=\"scenic.viewImage\" \r\n\t\t\t\tclass=\"hero-image\" \r\n\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t:style=\"{ transform: `scale(${1 + scrollY * 0.0005})` }\"\r\n\t\t\t></image>\r\n\t\t\t<view class=\"hero-overlay\">\r\n\t\t\t\t<view class=\"hero-gradient\"></view>\r\n\t\t\t\t<view class=\"hero-content\">\r\n\t\t\t\t\t<text class=\"hero-title\" :style=\"{ transform: `translateY(${titleOffset}px)`, opacity: titleOpacity }\">\r\n\t\t\t\t\t\t{{ scenic.name }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t\t<text class=\"hero-subtitle\" :style=\"{ transform: `translateY(${subtitleOffset}px)`, opacity: subtitleOpacity }\">\r\n\t\t\t\t\t\t{{ scenic.category }} · {{ scenic.era }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 浮动导航栏 -->\r\n\t\t\t<view class=\"floating-navbar\" :class=\"{ 'navbar-visible': showNavbar }\">\r\n\t\t\t\t<view class=\"navbar-content\">\r\n\t\t\t\t\t<!-- <view class=\"back-btn\" @click=\"goBack\">\r\n\t\t\t\t\t\t<text class=\"back-icon\">←</text>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<text class=\"navbar-title\">{{ scenic.name }}</text>\r\n\t\t\t\t\t<view class=\"share-btn\" @click=\"shareScenic\">\r\n\t\t\t\t\t\t<text class=\"share-icon\">⚡</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 滚动进度指示器 -->\r\n\t\t<view class=\"scroll-progress\">\r\n\t\t\t<view class=\"progress-bar\" :style=\"{ width: scrollProgress + '%' }\"></view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 卷轴内容区域 -->\r\n\t\t<scroll-view \r\n\t\t\tclass=\"scroll-content\" \r\n\t\t\tscroll-y=\"true\" \r\n\t\t\t@scroll=\"onScroll\"\r\n\t\t\t:scroll-top=\"scrollTopValue\"\r\n\t\t\tscroll-with-animation=\"true\"\r\n\t\t>\r\n\t\t\t<!-- 内容间隔 -->\r\n\t\t\t<view class=\"content-spacer\"></view>\r\n\t\t\t\r\n\t\t\t<!-- 卷轴展开动画区域 -->\r\n\t\t\t<view class=\"scroll-container\">\r\n\t\t\t\t\r\n\t\t\t\t<!-- 第一卷：基本信息 -->\r\n\t\t\t\t<view class=\"scroll-section intro-section\" :class=\"{ 'section-active': activeSection >= 0 }\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"section-number\">壹</view>\r\n\t\t\t\t\t\t<text class=\"section-title\">古韵初识</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"intro-card\">\r\n\t\t\t\t\t\t<view class=\"card-ornament top-left\"></view>\r\n\t\t\t\t\t\t<view class=\"card-ornament top-right\"></view>\r\n\t\t\t\t\t\t<view class=\"card-ornament bottom-left\"></view>\r\n\t\t\t\t\t\t<view class=\"card-ornament bottom-right\"></view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<view class=\"intro-content\">\r\n\t\t\t\t\t\t\t<text class=\"intro-text\">{{ scenic.introduction }}</text>\r\n\t\t\t\t\t\t\t<view class=\"intro-tags\">\r\n\t\t\t\t\t\t\t\t<text v-for=\"tag in scenic.tags\" :key=\"tag\" class=\"tag\">{{ tag }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 第二卷：历史传承 -->\r\n\t\t\t\t<view class=\"scroll-section history-section\" :class=\"{ 'section-active': activeSection >= 1 }\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"section-number\">贰</view>\r\n\t\t\t\t\t\t<text class=\"section-title\">历史传承</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"timeline-container\">\r\n\t\t\t\t\t\t<view class=\"timeline-line\"></view>\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tv-for=\"(period, index) in scenic.history\" \r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\tclass=\"timeline-item\"\r\n\t\t\t\t\t\t\t:class=\"{ 'timeline-active': activeTimelineItem >= index }\"\r\n\t\t\t\t\t\t\t:style=\"{ '--delay': index * 0.2 + 's' }\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<view class=\"timeline-dot\"></view>\r\n\t\t\t\t\t\t\t<view class=\"timeline-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"timeline-period\">{{ period.era }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"timeline-desc\">{{ period.description }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 第三卷：文化内涵 -->\r\n\t\t\t\t<view class=\"scroll-section culture-section\" :class=\"{ 'section-active': activeSection >= 2 }\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"section-number\">叁</view>\r\n\t\t\t\t\t\t<text class=\"section-title\">文化内涵</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"culture-grid\">\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tv-for=\"(aspect, index) in scenic.culture\" \r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\tclass=\"culture-card\"\r\n\t\t\t\t\t\t\t:class=\"{ 'card-active': activeCultureCard >= index }\"\r\n\t\t\t\t\t\t\t@click=\"showCultureDetail(aspect)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<view class=\"culture-icon\">{{ aspect.icon }}</view>\r\n\t\t\t\t\t\t\t<text class=\"culture-name\">{{ aspect.name }}</text>\r\n\t\t\t\t\t\t\t<text class=\"culture-desc\">{{ aspect.description }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 第四卷：今昔对比 -->\r\n\t\t\t\t<view class=\"scroll-section compare-section\" :class=\"{ 'section-active': activeSection >= 3 }\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"section-number\">肆</view>\r\n\t\t\t\t\t\t<text class=\"section-title\">古今对话</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"compare-container\">\r\n\t\t\t\t\t\t<view class=\"compare-slider\" :style=\"{ left: compareSliderPosition + '%' }\">\r\n\t\t\t\t\t\t\t<view class=\"slider-handle\"></view>\r\n\t\t\t\t\t\t\t<text class=\"slider-label\">{{ compareSliderPosition < 50 ? '古韵悠长' : '今日风华' }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"compare-images\" @touchmove=\"onCompareSlide\">\r\n\t\t\t\t\t\t\t<image :src=\"scenic.historicalImage\" class=\"compare-image historical\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t<image \r\n\t\t\t\t\t\t\t\t:src=\"scenic.viewImage\" \r\n\t\t\t\t\t\t\t\tclass=\"compare-image modern\" \r\n\t\t\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t\t\t:style=\"{ clipPath: `polygon(${compareSliderPosition}% 0%, 100% 0%, 100% 100%, ${compareSliderPosition}% 100%)` }\"\r\n\t\t\t\t\t\t\t></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 第五卷：游览指南 -->\r\n\t\t\t\t<view class=\"scroll-section guide-section\" :class=\"{ 'section-active': activeSection >= 4 }\">\r\n\t\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t\t<view class=\"section-number\">伍</view>\r\n\t\t\t\t\t\t<text class=\"section-title\">游览指南</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"guide-content\">\r\n\t\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t\t<view class=\"guide-icon\">🕐</view>\r\n\t\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t\t<text class=\"guide-label\">最佳时节</text>\r\n\t\t\t\t\t\t\t\t<text class=\"guide-value\">{{ scenic.bestTime }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t\t<view class=\"guide-icon\">🎫</view>\r\n\t\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t\t<text class=\"guide-label\">门票信息</text>\r\n\t\t\t\t\t\t\t\t<text class=\"guide-value\">{{ scenic.ticketInfo }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t\t<view class=\"guide-icon\">🚌</view>\r\n\t\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t\t<text class=\"guide-label\">交通指南</text>\r\n\t\t\t\t\t\t\t\t<text class=\"guide-value\">{{ scenic.transportation }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"guide-item\">\r\n\t\t\t\t\t\t\t<view class=\"guide-icon\">⭐</view>\r\n\t\t\t\t\t\t\t<view class=\"guide-text\">\r\n\t\t\t\t\t\t\t\t<text class=\"guide-label\">游览建议</text>\r\n\t\t\t\t\t\t\t\t<text class=\"guide-value\">{{ scenic.tips }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 底部操作区 -->\r\n\t\t\t\t<view class=\"bottom-actions\">\r\n\t\t\t\t\t<view class=\"action-grid\">\r\n\t\t\t\t\t\t<view class=\"action-item map-back-item\" @click=\"showOnMap\">\r\n\t\t\t\t\t\t\t<view class=\"action-icon map-icon\">🗺️</view>\r\n\t\t\t\t\t\t\t<text class=\"action-text\">地图定位</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"action-item\" @click=\"startVRExperience\">\r\n\t\t\t\t\t\t\t<view class=\"action-icon vr-icon\">🥽</view>\r\n\t\t\t\t\t\t\t<text class=\"action-text\">VR体验</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"action-item\" @click=\"shareScenic\">\r\n\t\t\t\t\t\t\t<view class=\"action-icon share-icon\">📤</view>\r\n\t\t\t\t\t\t\t<text class=\"action-text\">分享推荐</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"action-item\" @click=\"checkIn\">\r\n\t\t\t\t\t\t\t<view class=\"action-icon checkin-icon\" :class=\"{ 'checked-in': hasCheckedIn }\">印</view>\r\n\t\t\t\t\t\t\t<text class=\"action-text\">{{ hasCheckedIn ? '已打卡' : '盖章打卡' }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\r\n\t\t<!-- 文化详情弹窗 -->\r\n\t\t<view class=\"culture-modal\" v-if=\"showCultureModal\" @click=\"closeCultureModal\">\r\n\t\t\t<view class=\"modal-content\" @click.stop>\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<text class=\"modal-title\">{{ selectedCulture.name }}</text>\r\n\t\t\t\t\t<view class=\"modal-close\" @click=\"closeCultureModal\">×</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-body\">\r\n\t\t\t\t\t<text class=\"modal-text\">{{ selectedCulture.detailDescription }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { SCENIC_IMAGES } from '@/common/config.js';\r\nimport { database } from '@/common/database.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tscenicId: null,\r\n\t\t\tscrollY: 0,\r\n\t\t\tscrollTopValue: 0, \r\n\t\t\tactiveSection: -1,\r\n\t\t\tactiveTimelineItem: -1,\r\n\t\t\tactiveCultureCard: -1,\r\n\t\t\tshowNavbar: false,\r\n\t\t\tcompareSliderPosition: 50,\r\n\t\t\thasCheckedIn: false,\r\n\t\t\tshowCultureModal: false,\r\n\t\t\tselectedCulture: {},\r\n\t\t\ttouchStartY: 0,\r\n\t\t\tscenic: {}\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\theaderOffset() { return this.scrollY * 0.5 },\r\n\t\ttitleOffset() { return this.scrollY * 0.3 },\r\n\t\tsubtitleOffset() { return this.scrollY * 0.4 },\r\n\t\ttitleOpacity() { return Math.max(0, 1 - this.scrollY / 300) },\r\n\t\tsubtitleOpacity() { return Math.max(0, 1 - this.scrollY / 200) },\r\n\t\tscrollProgress() { return Math.min(100, (this.scrollY / 2000) * 100) }\r\n\t},\r\n\tonLoad(options) {\r\n\t\tif (options.id) {\r\n\t\t\tthis.scenicId = parseInt(options.id)\r\n\t\t\tthis.loadScenicData(this.scenicId)\r\n\t\t}\r\n\t\tthis.initAnimations()\r\n\t},\r\n\tmethods: {\r\n\t\tloadScenicData(id) {\r\n\t\t\tconst data = database.scenic[id];\r\n\t\t\tif (data) {\r\n\t\t\t\tconst scenicData = { ...data };\r\n\t\t\t\tscenicData.viewImage = SCENIC_IMAGES[scenicData.folder].view;\r\n\t\t\t\tscenicData.historicalImage = SCENIC_IMAGES[scenicData.folder].index;\r\n\t\t\t\tthis.scenic = scenicData;\r\n\t\t\t\tthis.checkFootprintStatus();\r\n\t\t\t}\r\n\t\t},\r\n\t\tinitAnimations() {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.activeSection = 4;\r\n\t\t\t\tthis.activeTimelineItem = 3;\r\n\t\t\t\tthis.activeCultureCard = 3;\r\n\t\t\t}, 500);\r\n\t\t},\r\n\t\tonScroll(e) {\r\n\t\t\tthis.scrollY = e.detail.scrollTop;\r\n\t\t\tthis.showNavbar = this.scrollY > 200;\r\n\t\t},\r\n\t\tonTouchStart(e) { this.touchStartY = e.touches[0].clientY },\r\n\t\tonCompareSlide(e) {\r\n\t\t\tconst touch = e.touches[0];\r\n\t\t\tconst percentage = (touch.clientX / (uni.getSystemInfoSync().windowWidth)) * 100;\r\n\t\t\tthis.compareSliderPosition = Math.max(0, Math.min(100, percentage));\r\n\t\t},\r\n\t\tshowCultureDetail(culture) {\r\n\t\t\tthis.selectedCulture = culture;\r\n\t\t\tthis.showCultureModal = true;\r\n\t\t},\r\n\t\tcloseCultureModal() { this.showCultureModal = false },\r\n\t\tstartVRExperience() { uni.navigateTo({ url: `/pages/vr/experience?scenicId=${this.scenic.id}&name=${encodeURIComponent(this.scenic.name)}` }); },\r\n\t\tshowOnMap() { uni.navigateTo({ url: `/pages/map/map?scenicId=${this.scenic.id}` }); },\r\n\t\tshareScenic() { uni.showToast({ title: '分享功能开发中', icon: 'none' }); },\r\n\t\tcheckIn() {\r\n\t\t\tconst key = `scenic-${this.scenic.id}`;\r\n\t\t\tconst checkIns = uni.getStorageSync('checkIns') || {};\r\n\r\n\t\t\tif (this.hasCheckedIn) {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '取消打卡',\r\n\t\t\t\t\tcontent: '确定要抹除这个足迹吗？',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tdelete checkIns[key];\r\n\t\t\t\t\t\t\tuni.setStorageSync('checkIns', checkIns);\r\n\t\t\t\t\t\t\tthis.hasCheckedIn = false;\r\n\t\t\t\t\t\t\tuni.showToast({ title: '足迹已抹除', icon: 'none' });\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tcheckIns[key] = { id: this.scenic.id, type: 'scenic', name: this.scenic.name, checkedInAt: new Date().toISOString() };\r\n\t\t\t\tuni.setStorageSync('checkIns', checkIns);\r\n\t\t\t\tthis.hasCheckedIn = true;\r\n\t\t\t\tuni.showToast({ title: '盖章成功！', icon: 'success' });\r\n\t\t\t}\r\n\t\t},\r\n\t\tcheckFootprintStatus() {\r\n\t\t\tconst checkIns = uni.getStorageSync('checkIns') || {};\r\n\t\t\tconst key = `scenic-${this.scenic.id}`;\r\n\t\t\tthis.hasCheckedIn = !!checkIns[key];\r\n\t\t},\r\n\t\tgoBack() { uni.navigateBack(); }\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.back-button {\r\n\tposition: absolute;\r\n\ttop: calc(var(--status-bar-height) + 20rpx);\r\n\tleft: 30rpx;\r\n\tz-index: 1000;\r\n\twidth: 70rpx;\r\n\theight: 70rpx;\r\n\tbackground-color: rgba(0, 0, 0, 0.4);\r\n\tborder-radius: 50%;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tbackdrop-filter: blur(5px);\r\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\r\n\r\n\t.back-icon {\r\n\t\tfont-size: 50rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n}\r\n\r\n// 全局样式重置\r\n.detail-container {\r\n\twidth: 100vw;\r\n\tmin-height: 100vh;\r\n\tbackground: linear-gradient(180deg, #2d1b1b 0%, #4a3226 50%, #3d2817 100%);\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n// 沉浸式头部\r\n.immersive-header {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\theight: 100vh;\r\n\tz-index: 10;\r\n\t\r\n\t.hero-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tobject-fit: cover;\r\n\t}\r\n\t\r\n\t.hero-overlay {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\t\r\n\t\t.hero-gradient {\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tright: 0;\r\n\t\t\theight: 60%;\r\n\t\t\tbackground: linear-gradient(transparent, rgba(0, 0, 0, 0.7));\r\n\t\t}\r\n\t\t\r\n\t\t.hero-content {\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 150rpx;\r\n\t\t\tleft: 60rpx;\r\n\t\t\tright: 60rpx;\r\n\t\t\t\r\n\t\t\t.hero-title {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tfont-size: 80rpx;\r\n\t\t\t\tcolor: #FFD700;\r\n\t\t\t\tfont-weight: 900;\r\n\t\t\t\ttext-shadow: 2rpx 2rpx 8rpx rgba(0, 0, 0, 0.8);\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\tletter-spacing: 4rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.hero-subtitle {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #FFF8DC;\r\n\t\t\t\ttext-shadow: 1rpx 1rpx 4rpx rgba(0, 0, 0, 0.6);\r\n\t\t\t\tletter-spacing: 2rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 浮动导航栏\r\n.floating-navbar {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground: rgba(45, 27, 27, 0.9);\r\n\tbackdrop-filter: blur(20rpx);\r\n\tborder-bottom: 1rpx solid rgba(255, 215, 0, 0.3);\r\n\ttransform: translateY(-100%);\r\n\ttransition: transform 0.3s ease;\r\n\tz-index: 1000;\r\n\t\r\n\t&.navbar-visible {\r\n\t\ttransform: translateY(0);\r\n\t}\r\n\t\r\n\t.navbar-content {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-around;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tpadding-top: calc(20rpx + var(--status-bar-height, 0));\r\n\t\t\r\n\t\t.back-btn, .share-btn {\r\n\t\t\twidth: 60rpx;\r\n\t\t\theight: 60rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tbackground: rgba(255, 215, 0, 0.1);\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tborder: 1rpx solid rgba(255, 215, 0, 0.3);\r\n\t\t\t\r\n\t\t\t.back-icon, .share-icon {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #FFD700;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.navbar-title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #FFD700;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tmax-width: 400rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\toverflow: hidden;\r\n\t\t\ttext-overflow: ellipsis;\r\n\t\t\twhite-space: nowrap;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 滚动进度指示器\r\n.scroll-progress {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\theight: 6rpx;\r\n\tbackground: rgba(255, 215, 0, 0.2);\r\n\tz-index: 1001;\r\n\t\r\n\t.progress-bar {\r\n\t\theight: 100%;\r\n\t\tbackground: linear-gradient(90deg, #FFD700, #FFA500);\r\n\t\ttransition: width 0.1s ease;\r\n\t\tbox-shadow: 0 0 10rpx rgba(255, 215, 0, 0.5);\r\n\t}\r\n}\r\n\r\n// 滚动内容区域\r\n.scroll-content {\r\n\tposition: relative;\r\n\tz-index: 20;\r\n\tbackground: transparent;\r\n}\r\n\r\n.content-spacer {\r\n\theight: 100vh;\r\n}\r\n\r\n.scroll-container {\r\n\tbackground: linear-gradient(180deg, transparent 0%, rgba(45, 27, 27, 0.95) 10%, rgba(61, 40, 23, 0.98) 20%);\r\n\tmin-height: 100vh;\r\n\tpadding: 60rpx 0;\r\n}\r\n\r\n// 卷轴章节样式\r\n.scroll-section {\r\n\tmargin-bottom: 120rpx;\r\n\tpadding: 0 60rpx;\r\n\topacity: 0;\r\n\ttransform: translateY(100rpx);\r\n\ttransition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);\r\n\t\r\n\t&.section-active {\r\n\t\topacity: 1;\r\n\t\ttransform: translateY(0);\r\n\t}\r\n\t\r\n\t.section-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 60rpx;\r\n\t\t\r\n\t\t.section-number {\r\n\t\t\twidth: 80rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\tbackground: linear-gradient(135deg, #FFD700, #FFA500);\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #1a1a1a;\r\n\t\t\tmargin-right: 30rpx;\r\n\t\t\tbox-shadow: 0 8rpx 20rpx rgba(255, 215, 0, 0.3);\r\n\t\t}\r\n\t\t\r\n\t\t.section-title {\r\n\t\t\tfont-size: 48rpx;\r\n\t\t\tcolor: #FFD700;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tletter-spacing: 4rpx;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 介绍卡片样式\r\n.intro-card {\r\n\tposition: relative;\r\n\tbackground: rgba(74, 50, 38, 0.85);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 60rpx;\r\n\tborder: 2rpx solid rgba(255, 215, 0, 0.3);\r\n\tbox-shadow: 0 20rpx 40rpx rgba(45, 27, 27, 0.4);\r\n\t\r\n\t.card-ornament {\r\n\t\tposition: absolute;\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder: 4rpx solid #FFD700;\r\n\t\t\r\n\t\t&.top-left {\r\n\t\t\ttop: 20rpx;\r\n\t\t\tleft: 20rpx;\r\n\t\t\tborder-right: none;\r\n\t\t\tborder-bottom: none;\r\n\t\t}\r\n\t\t\r\n\t\t&.top-right {\r\n\t\t\ttop: 20rpx;\r\n\t\t\tright: 20rpx;\r\n\t\t\tborder-left: none;\r\n\t\t\tborder-bottom: none;\r\n\t\t}\r\n\t\t\r\n\t\t&.bottom-left {\r\n\t\t\tbottom: 20rpx;\r\n\t\t\tleft: 20rpx;\r\n\t\t\tborder-right: none;\r\n\t\t\tborder-top: none;\r\n\t\t}\r\n\t\t\r\n\t\t&.bottom-right {\r\n\t\t\tbottom: 20rpx;\r\n\t\t\tright: 20rpx;\r\n\t\t\tborder-left: none;\r\n\t\t\tborder-top: none;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.intro-content {\r\n\t\t.intro-text {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tline-height: 1.8;\r\n\t\t\tcolor: #FFF8DC;\r\n\t\t\tmargin-bottom: 40rpx;\r\n\t\t\ttext-indent: 2em;\r\n\t\t}\r\n\t\t\r\n\t\t.intro-tags {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\tgap: 20rpx;\r\n\t\t\t\r\n\t\t\t.tag {\r\n\t\t\t\tpadding: 12rpx 24rpx;\r\n\t\t\t\tbackground: rgba(255, 215, 0, 0.1);\r\n\t\t\t\tborder: 1rpx solid rgba(255, 215, 0, 0.3);\r\n\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #FFD700;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 时间轴样式\r\n.timeline-container {\r\n\tposition: relative;\r\n\t\r\n\t.timeline-line {\r\n\t\tposition: absolute;\r\n\t\tleft: 40rpx;\r\n\t\ttop: 0;\r\n\t\tbottom: 0;\r\n\t\twidth: 4rpx;\r\n\t\tbackground: linear-gradient(180deg, #FFD700, #FFA500);\r\n\t\tborder-radius: 2rpx;\r\n\t}\r\n\t\r\n\t.timeline-item {\r\n\t\tposition: relative;\r\n\t\tpadding-left: 120rpx;\r\n\t\tmargin-bottom: 60rpx;\r\n\t\topacity: 0;\r\n\t\ttransform: translateX(-50rpx);\r\n\t\ttransition: all 0.6s ease;\r\n\t\ttransition-delay: var(--delay);\r\n\t\t\r\n\t\t&.timeline-active {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: translateX(0);\r\n\t\t}\r\n\t\t\r\n\t\t.timeline-dot {\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 28rpx;\r\n\t\t\ttop: 10rpx;\r\n\t\t\twidth: 24rpx;\r\n\t\t\theight: 24rpx;\r\n\t\t\tbackground: #FFD700;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbox-shadow: 0 0 20rpx rgba(255, 215, 0, 0.5);\r\n\t\t}\r\n\t\t\r\n\t\t.timeline-content {\r\n\t\t\tbackground: rgba(44, 24, 16, 0.6);\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tborder-radius: 15rpx;\r\n\t\t\tborder-left: 4rpx solid #FFD700;\r\n\t\t\t\r\n\t\t\t.timeline-period {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #FFD700;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.timeline-desc {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #FFF8DC;\r\n\t\t\t\tline-height: 1.6;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 文化网格样式\r\n.culture-grid {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: 1fr 1fr;\r\n\tgap: 30rpx;\r\n\t\r\n\t.culture-card {\r\n\t\tbackground: rgba(44, 24, 16, 0.6);\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 40rpx;\r\n\t\ttext-align: center;\r\n\t\tborder: 2rpx solid transparent;\r\n\t\ttransition: all 0.4s ease;\r\n\t\ttransform: scale(0.95);\r\n\t\topacity: 0;\r\n\t\t\r\n\t\t&.card-active {\r\n\t\t\topacity: 1;\r\n\t\t\ttransform: scale(1);\r\n\t\t}\r\n\t\t\r\n\t\t&:active {\r\n\t\t\ttransform: scale(0.98);\r\n\t\t\tborder-color: #FFD700;\r\n\t\t\tbackground: rgba(44, 24, 16, 0.8);\r\n\t\t}\r\n\t\t\r\n\t\t.culture-icon {\r\n\t\t\tfont-size: 60rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.culture-name {\r\n\t\t\tdisplay: block;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #FFD700;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tmargin-bottom: 15rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.culture-desc {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #FFF8DC;\r\n\t\t\tline-height: 1.5;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 对比区域样式\r\n.compare-container {\r\n\tposition: relative;\r\n\theight: 400rpx;\r\n\tborder-radius: 20rpx;\r\n\toverflow: hidden;\r\n\t\r\n\t.compare-slider {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\tbottom: 0;\r\n\t\twidth: 4rpx;\r\n\t\tbackground: #FFD700;\r\n\t\tz-index: 10;\r\n\t\ttransition: left 0.1s ease;\r\n\t\t\r\n\t\t.slider-handle {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 50%;\r\n\t\t\tleft: -15rpx;\r\n\t\t\twidth: 34rpx;\r\n\t\t\theight: 34rpx;\r\n\t\t\tbackground: #FFD700;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\ttransform: translateY(-50%);\r\n\t\t\tbox-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.4);\r\n\t\t}\r\n\t\t\r\n\t\t.slider-label {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 20rpx;\r\n\t\t\tleft: -60rpx;\r\n\t\t\twidth: 120rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #FFD700;\r\n\t\t\tbackground: rgba(26, 26, 26, 0.8);\r\n\t\t\tpadding: 8rpx;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.compare-images {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\t\r\n\t\t.compare-image {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tobject-fit: cover;\r\n\t\t\t\r\n\t\t\t&.modern {\r\n\t\t\t\tz-index: 5;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 指南样式\r\n.guide-content {\r\n\t.guide-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground: rgba(44, 24, 16, 0.6);\r\n\t\tborder-radius: 15rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tborder-left: 4rpx solid #FFD700;\r\n\t\t\r\n\t\t.guide-icon {\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\tmargin-right: 30rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.guide-text {\r\n\t\t\tflex: 1;\r\n\t\t\t\r\n\t\t\t.guide-label {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #FFD700;\r\n\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.guide-value {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #FFF8DC;\r\n\t\t\t\tline-height: 1.5;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 底部操作区\r\n.bottom-actions {\r\n\tmargin-top: 40rpx;\r\n\t\r\n\t.action-grid {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: 1fr 1fr;\r\n\t\tgap: 30rpx;\r\n\t\tpadding: 0 60rpx;\r\n\t\t\r\n\t\t.action-item {\r\n\t\t\tbackground: rgba(44, 24, 16, 0.8);\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tpadding: 40rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tborder: 2rpx solid rgba(255, 215, 0, 0.2);\r\n\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\r\n\t\t\t&.map-back-item {\r\n\t\t\t\tbackground: linear-gradient(135deg, #FFD700, #FFA500);\r\n\t\t\t\tborder: 3rpx solid #8B4513;\r\n\t\t\t\ttransform: scale(1.05);\r\n\t\t\t\t\r\n\t\t\t\t.action-icon {\r\n\t\t\t\t\tfont-size: 60rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.action-text {\r\n\t\t\t\t\tcolor: #1a1a1a;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttransform: scale(1.0);\r\n\t\t\t\t\tbackground: linear-gradient(135deg, #FFA500, #FF8C00);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&:active {\r\n\t\t\t\ttransform: scale(0.95);\r\n\t\t\t\tbackground: rgba(44, 24, 16, 0.9);\r\n\t\t\t\tborder-color: #FFD700;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.action-icon {\r\n\t\t\t\tfont-size: 50rpx;\r\n\t\t\t\tmargin-bottom: 15rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.action-text {\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #FFF8DC;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 文化详情弹窗\r\n.culture-modal {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0, 0, 0, 0.8);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 2000;\r\n\tpadding: 60rpx;\r\n\t\r\n\t.modal-content {\r\n\t\tbackground: #1a1a1a;\r\n\t\tborder-radius: 20rpx;\r\n\t\tborder: 2rpx solid #FFD700;\r\n\t\tmax-width: 600rpx;\r\n\t\tmax-height: 80vh;\r\n\t\toverflow: hidden;\r\n\t\t\r\n\t\t.modal-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tpadding: 30rpx 40rpx;\r\n\t\t\tborder-bottom: 1rpx solid rgba(255, 215, 0, 0.2);\r\n\t\t\t\r\n\t\t\t.modal-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #FFD700;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.modal-close {\r\n\t\t\t\twidth: 50rpx;\r\n\t\t\t\theight: 50rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tcolor: #FFD700;\r\n\t\t\t\tbackground: rgba(255, 215, 0, 0.1);\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.modal-body {\r\n\t\t\tpadding: 40rpx;\r\n\t\t\t\r\n\t\t\t.modal-text {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #FFF8DC;\r\n\t\t\t\tline-height: 1.8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.checkin-icon {\r\n\tbackground-color: #f0f0f0;\r\n\tcolor: #8B4513;\r\n\tfont-weight: bold;\r\n\tborder: 2rpx solid #8B4513;\r\n\r\n\t&.checked-in {\r\n\t\tbackground-color: #8B4513;\r\n\t\tcolor: #FFD700;\r\n\t\ttransform: scale(1.1) rotate(15deg);\r\n\t\tbox-shadow: 0 0 15rpx #FFD700;\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/Users/<USER>/Desktop/yunVr/pages/scenic/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["database", "SCENIC_IMAGES", "uni"], "mappings": ";;;;AAoOA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,iBAAiB,CAAE;AAAA,MACnB,aAAa;AAAA,MACb,QAAQ,CAAC;AAAA,IACV;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,eAAe;AAAE,aAAO,KAAK,UAAU;AAAA,IAAK;AAAA,IAC5C,cAAc;AAAE,aAAO,KAAK,UAAU;AAAA,IAAK;AAAA,IAC3C,iBAAiB;AAAE,aAAO,KAAK,UAAU;AAAA,IAAK;AAAA,IAC9C,eAAe;AAAE,aAAO,KAAK,IAAI,GAAG,IAAI,KAAK,UAAU,GAAG;AAAA,IAAG;AAAA,IAC7D,kBAAkB;AAAE,aAAO,KAAK,IAAI,GAAG,IAAI,KAAK,UAAU,GAAG;AAAA,IAAG;AAAA,IAChE,iBAAiB;AAAE,aAAO,KAAK,IAAI,KAAM,KAAK,UAAU,MAAQ,GAAG;AAAA,IAAE;AAAA,EACrE;AAAA,EACD,OAAO,SAAS;AACf,QAAI,QAAQ,IAAI;AACf,WAAK,WAAW,SAAS,QAAQ,EAAE;AACnC,WAAK,eAAe,KAAK,QAAQ;AAAA,IAClC;AACA,SAAK,eAAe;AAAA,EACpB;AAAA,EACD,SAAS;AAAA,IACR,eAAe,IAAI;AAClB,YAAM,OAAOA,gBAAAA,SAAS,OAAO,EAAE;AAC/B,UAAI,MAAM;AACT,cAAM,aAAa,EAAE,GAAG;AACxB,mBAAW,YAAYC,cAAa,cAAC,WAAW,MAAM,EAAE;AACxD,mBAAW,kBAAkBA,cAAa,cAAC,WAAW,MAAM,EAAE;AAC9D,aAAK,SAAS;AACd,aAAK,qBAAoB;AAAA,MAC1B;AAAA,IACA;AAAA,IACD,iBAAiB;AAChB,iBAAW,MAAM;AAChB,aAAK,gBAAgB;AACrB,aAAK,qBAAqB;AAC1B,aAAK,oBAAoB;AAAA,MACzB,GAAE,GAAG;AAAA,IACN;AAAA,IACD,SAAS,GAAG;AACX,WAAK,UAAU,EAAE,OAAO;AACxB,WAAK,aAAa,KAAK,UAAU;AAAA,IACjC;AAAA,IACD,aAAa,GAAG;AAAE,WAAK,cAAc,EAAE,QAAQ,CAAC,EAAE;AAAA,IAAS;AAAA,IAC3D,eAAe,GAAG;AACjB,YAAM,QAAQ,EAAE,QAAQ,CAAC;AACzB,YAAM,aAAc,MAAM,UAAWC,cAAG,MAAC,kBAAmB,EAAC,cAAgB;AAC7E,WAAK,wBAAwB,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,UAAU,CAAC;AAAA,IAClE;AAAA,IACD,kBAAkB,SAAS;AAC1B,WAAK,kBAAkB;AACvB,WAAK,mBAAmB;AAAA,IACxB;AAAA,IACD,oBAAoB;AAAE,WAAK,mBAAmB;AAAA,IAAO;AAAA,IACrD,oBAAoB;AAAEA,oBAAAA,MAAI,WAAW,EAAE,KAAK,iCAAiC,KAAK,OAAO,EAAE,SAAS,mBAAmB,KAAK,OAAO,IAAI,CAAC,GAAI,CAAA;AAAA,IAAI;AAAA,IAChJ,YAAY;AAAEA,oBAAAA,MAAI,WAAW,EAAE,KAAK,2BAA2B,KAAK,OAAO,EAAE,IAAI;AAAA,IAAI;AAAA,IACrF,cAAc;AAAEA,0BAAI,UAAU,EAAE,OAAO,WAAW,MAAM,OAAQ,CAAA;AAAA,IAAI;AAAA,IACpE,UAAU;AACT,YAAM,MAAM,UAAU,KAAK,OAAO,EAAE;AACpC,YAAM,WAAWA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AAEnD,UAAI,KAAK,cAAc;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAChB,qBAAO,SAAS,GAAG;AACnBA,4BAAAA,MAAI,eAAe,YAAY,QAAQ;AACvC,mBAAK,eAAe;AACpBA,4BAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,OAAK,CAAG;AAAA,YAC/C;AAAA,UACD;AAAA,QACD,CAAC;AAAA,aACK;AACN,iBAAS,GAAG,IAAI,EAAE,IAAI,KAAK,OAAO,IAAI,MAAM,UAAU,MAAM,KAAK,OAAO,MAAM,cAAa,oBAAI,KAAM,GAAC,YAAW;AACjHA,sBAAAA,MAAI,eAAe,YAAY,QAAQ;AACvC,aAAK,eAAe;AACpBA,sBAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,UAAQ,CAAG;AAAA,MAClD;AAAA,IACA;AAAA,IACD,uBAAuB;AACtB,YAAM,WAAWA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,YAAM,MAAM,UAAU,KAAK,OAAO,EAAE;AACpC,WAAK,eAAe,CAAC,CAAC,SAAS,GAAG;AAAA,IAClC;AAAA,IACD,SAAS;AAAEA,oBAAAA,MAAI,aAAY;AAAA,IAAI;AAAA,EAChC;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvUA,GAAG,WAAW,eAAe;"}