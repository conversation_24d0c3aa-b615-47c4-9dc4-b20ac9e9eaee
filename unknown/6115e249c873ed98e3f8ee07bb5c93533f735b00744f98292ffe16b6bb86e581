{"version": 3, "file": "index.js", "sources": ["pages/detail/index.vue", "../../../../App/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZGV0YWlsL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"detail-container\">\r\n\t\t<!-- 动态渲染组件 -->\r\n\t\t<ScenicContent v-if=\"type === 'scenic'\" :content=\"contentData\" @checkIn=\"handleCheckIn\" />\r\n\t\t<FoodContent v-else-if=\"type === 'food'\" :content=\"contentData\" @checkIn=\"handleCheckIn\" />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { SCENIC_IMAGES, FOOD_IMAGES } from '@/common/config.js';\r\nimport { database } from '@/common/database.js';\r\nimport ScenicContent from './components/ScenicContent.vue';\r\nimport FoodContent from './components/FoodContent.vue';\r\n\r\nexport default {\r\n\tcomponents: {\r\n\t\tScenicContent,\r\n\t\tFoodContent\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\ttype: null,\r\n\t\t\tcontentId: null,\r\n\t\t\tcontentData: null\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\tif (options.id && options.type) {\r\n\t\t\tthis.type = options.type;\r\n\t\t\tthis.contentId = options.id;\r\n\t\t\tthis.loadContentData();\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tloadContentData() {\r\n\t\t\tlet data = {};\r\n\t\t\tif (this.type === 'scenic' && database.scenic[this.contentId]) {\r\n\t\t\t\tdata = { ...database.scenic[this.contentId] };\r\n\t\t\t\tdata.viewImage = SCENIC_IMAGES[data.folder].view;\r\n\t\t\t\tdata.historicalImage = SCENIC_IMAGES[data.folder].index;\r\n\t\t\t} else if (this.type === 'food' && database.food[this.contentId]) {\r\n\t\t\t\tdata = { ...database.food[this.contentId] };\r\n\t\t\t\tdata.viewImage = FOOD_IMAGES[this.contentId];\r\n\t\t\t\tdata.historicalImage = FOOD_IMAGES.index;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 检查打卡状态\r\n\t\t\tconst checkIns = uni.getStorageSync('checkIns') || {};\r\n\t\t\tconst key = `${this.type}-${this.contentId}`;\r\n\t\t\tdata.hasCheckedIn = !!checkIns[key];\r\n\r\n\t\t\tthis.contentData = data;\r\n\t\t},\r\n\t\thandleCheckIn(item) {\r\n\t\t\tconst checkIns = uni.getStorageSync('checkIns') || {};\r\n\t\t\tconst key = `${item.type}-${item.id}`;\r\n\t\t\t\r\n\t\t\tif (item.hasCheckedIn) {\r\n\t\t\t\t// 取消打卡\r\n\t\t\t\tdelete checkIns[key];\r\n\t\t\t\tuni.showToast({ title: '已取消', icon: 'none' });\r\n\t\t\t} else {\r\n\t\t\t\t// 打卡\r\n\t\t\t\tcheckIns[key] = {\r\n\t\t\t\t\tid: item.id,\r\n\t\t\t\t\ttype: item.type,\r\n\t\t\t\t\tname: item.name,\r\n\t\t\t\t\tcheckedInAt: new Date().toISOString()\r\n\t\t\t\t};\r\n\t\t\t\tuni.showToast({ title: '操作成功', icon: 'success' });\r\n\t\t\t}\r\n\t\t\tuni.setStorageSync('checkIns', checkIns);\r\n\t\t\t\r\n\t\t\t// 重新加载数据以更新状态\r\n\t\t\tthis.loadContentData();\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.detail-container {\r\n\twidth: 100vw;\r\n\theight: 100vh;\r\n\tbackground-color: #1a1a1a;\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/Users/<USER>/Desktop/yunVr/pages/detail/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["database", "SCENIC_IMAGES", "FOOD_IMAGES", "uni"], "mappings": ";;;;AAWA,MAAK,gBAAiB,MAAW;AACjC,oBAAoB,MAAW;AAE/B,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,IACA;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,MACX,aAAa;AAAA,IACd;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,QAAI,QAAQ,MAAM,QAAQ,MAAM;AAC/B,WAAK,OAAO,QAAQ;AACpB,WAAK,YAAY,QAAQ;AACzB,WAAK,gBAAe;AAAA,IACrB;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,kBAAkB;AACjB,UAAI,OAAO,CAAA;AACX,UAAI,KAAK,SAAS,YAAYA,gBAAQ,SAAC,OAAO,KAAK,SAAS,GAAG;AAC9D,eAAO,EAAE,GAAGA,gBAAQ,SAAC,OAAO,KAAK,SAAS;AAC1C,aAAK,YAAYC,cAAa,cAAC,KAAK,MAAM,EAAE;AAC5C,aAAK,kBAAkBA,cAAa,cAAC,KAAK,MAAM,EAAE;AAAA,iBACxC,KAAK,SAAS,UAAUD,gBAAQ,SAAC,KAAK,KAAK,SAAS,GAAG;AACjE,eAAO,EAAE,GAAGA,gBAAQ,SAAC,KAAK,KAAK,SAAS;AACxC,aAAK,YAAYE,cAAAA,YAAY,KAAK,SAAS;AAC3C,aAAK,kBAAkBA,cAAW,YAAC;AAAA,MACpC;AAGA,YAAM,WAAWC,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,YAAM,MAAM,GAAG,KAAK,IAAI,IAAI,KAAK,SAAS;AAC1C,WAAK,eAAe,CAAC,CAAC,SAAS,GAAG;AAElC,WAAK,cAAc;AAAA,IACnB;AAAA,IACD,cAAc,MAAM;AACnB,YAAM,WAAWA,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AACnD,YAAM,MAAM,GAAG,KAAK,IAAI,IAAI,KAAK,EAAE;AAEnC,UAAI,KAAK,cAAc;AAEtB,eAAO,SAAS,GAAG;AACnBA,sBAAG,MAAC,UAAU,EAAE,OAAO,OAAO,MAAM,OAAK,CAAG;AAAA,aACtC;AAEN,iBAAS,GAAG,IAAI;AAAA,UACf,IAAI,KAAK;AAAA,UACT,MAAM,KAAK;AAAA,UACX,MAAM,KAAK;AAAA,UACX,cAAa,oBAAI,KAAM,GAAC,YAAY;AAAA;AAErCA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,UAAQ,CAAG;AAAA,MACjD;AACAA,oBAAAA,MAAI,eAAe,YAAY,QAAQ;AAGvC,WAAK,gBAAe;AAAA,IACrB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;AC5EA,GAAG,WAAW,eAAe;"}