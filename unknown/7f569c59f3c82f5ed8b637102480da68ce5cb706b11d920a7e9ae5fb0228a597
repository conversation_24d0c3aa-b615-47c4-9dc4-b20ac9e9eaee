/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.star-chart-container {
  background-color: #0c0a18;
  background-image: radial-gradient(circle at 20% 20%, rgba(100, 100, 150, 0.3) 0%, transparent 40%), radial-gradient(circle at 80% 70%, rgba(150, 100, 100, 0.2) 0%, transparent 30%);
  min-height: 100vh;
  color: #e0e0e0;
  padding: 0 40rpx 40rpx 40rpx;
  box-sizing: border-box;
  padding-top: var(--status-bar-height);
}
.star-chart-container .back-button {
  position: absolute;
  top: calc(var(--status-bar-height) + 20rpx);
  left: 30rpx;
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all 0.3s ease;
}
.star-chart-container .back-button .back-icon {
  font-size: 50rpx;
  color: #a0a0c0;
  font-weight: bold;
  line-height: 70rpx;
}
.star-chart-container .back-button:active {
  background: rgba(255, 215, 0, 0.2);
  box-shadow: 0 0 15rpx rgba(255, 215, 0, 0.5);
}
.star-chart-container .header {
  padding-top: 20rpx;
  margin-bottom: 60rpx;
  text-align: center;
}
.star-chart-container .title {
  font-size: 52rpx;
  font-weight: bold;
  color: #FFD700;
  text-shadow: 0 0 10rpx #FFD700;
  display: block;
  margin-bottom: 10rpx;
  font-family: "KaiTi", "STKaiti", serif;
}
.star-chart-container .progress-text {
  font-size: 28rpx;
  color: #a0a0c0;
  margin-bottom: 30rpx;
}
.star-chart-container .progress-bar {
  border-radius: 4rpx;
  overflow: hidden;
}
.star-chart-container .star-map {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  /* 改为两列以适应不同形状 */
  gap: 40rpx;
}
.star-chart-container .grid-item {
  position: relative;
  height: 280rpx;
  transition: all 0.4s ease-in-out;
  background-color: #333;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.5);
}
.star-chart-container .grid-item.scenic {
  border-radius: 50%;
}
.star-chart-container .grid-item.food {
  border-radius: 16rpx;
}
.star-chart-container .grid-item .item-image {
  width: 100%;
  height: 100%;
  filter: grayscale(100%) brightness(0.4);
  transition: all 0.5s ease;
}
.star-chart-container .grid-item.scenic .item-image {
  border-radius: 50%;
}
.star-chart-container .grid-item.food .item-image {
  border-radius: 16rpx;
}
.star-chart-container .grid-item .item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, transparent 50%, rgba(0, 0, 0, 0.8) 100%);
}
.star-chart-container .grid-item.scenic .item-overlay {
  border-radius: 50%;
}
.star-chart-container .grid-item.food .item-overlay {
  border-radius: 16rpx;
}
.star-chart-container .grid-item .item-name {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  text-align: center;
  font-size: 28rpx;
  color: #bbb;
  font-weight: bold;
  transition: color 0.5s ease;
}
.star-chart-container .grid-item .lock-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24rpx;
  color: rgba(255, 215, 0, 0.4);
  background-color: rgba(0, 0, 0, 0.5);
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  text-shadow: 0 0 5rpx rgba(0, 0, 0, 0.5);
  transition: all 0.5s ease;
}
.star-chart-container .grid-item.unlocked .item-image {
  filter: grayscale(0%) brightness(1);
}
.star-chart-container .grid-item.unlocked.scenic .item-image {
  box-shadow: 0 0 15rpx 5rpx rgba(255, 215, 0, 0.4), 0 0 30rpx 10rpx rgba(255, 215, 0, 0.2);
}
.star-chart-container .grid-item.unlocked.food .item-image {
  box-shadow: 0 0 15rpx 5rpx rgba(100, 255, 100, 0.4);
}
.star-chart-container .grid-item.unlocked .item-name {
  color: #FFD700;
}
.star-chart-container .grid-item.unlocked .lock-icon {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0);
}
.star-chart-container .grid-item:active {
  transform: scale(0.95);
}
.action-button-container {
  margin-top: 80rpx;
  text-align: center;
}
.portal-button {
  display: inline-block;
  background: radial-gradient(ellipse at center, rgba(255, 215, 0, 0.8) 0%, rgba(255, 215, 0, 0) 70%);
  color: #fff;
  font-weight: bold;
  padding: 25rpx 70rpx;
  border-radius: 50rpx;
  box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.5);
  border: 1rpx solid rgba(255, 215, 0, 0.3);
  transition: all 0.3s ease;
}
.portal-button:active {
  transform: scale(0.98);
  box-shadow: 0 0 30rpx rgba(255, 215, 0, 0.8);
}