# 衡阳古韵 - 文化旅游微信小程序

## 项目简介

衡阳古韵是一个展示衡阳本地文化旅游资源的uni-app微信小程序，采用沉浸式古韵设计风格，集成地图导航、VR体验、景点详情等功能，为用户提供全方位的衡阳文化旅游体验。

## ✨ 核心特色

- 🎨 **沉浸式古韵设计** - 古代卷轴展开效果，视差滚动体验
- ☁️ **云端图片存储** - 阿里云OSS + CDN加速，突破小程序包大小限制
- ✨ **个人足迹系统** - 点亮衡阳星宿图，记录你的文化旅程
- 🤖 **AI智能科考** - 与AI考官对答，检验你的衡阳知识
- 🗺️ **智能地图导航** - 腾讯地图API集成，实时定位和路线规划
- 📱 **移动端优化** - 完美适配微信小程序，流畅的手机端体验
- 🏛️ **文化内涵丰富** - 深度展示衡阳六大景点的历史文化底蕴

## 🛠️ 技术栈

### 前端技术

- **框架**: uni-app + Vue.js 2.x
- **AI模型**: 智谱 GLM-4 (前端直连)
- **样式**: SCSS + 原生CSS3动画
- **地图**: 腾讯地图API
- **图片存储**: 阿里云OSS + CDN
- **构建工具**: HBuilderX + Vite

### 云端服务

- **图片存储**: 阿里云OSS (北京节点)
- **CDN加速**: 阿里云CDN全国加速
- **HTTPS安全**: SSL证书保障

## 📁 项目结构

yunVr/
├── pages/                    # 页面文件
│   ├── index/               # 首页 - 古韵轮播展示
│   │   └── index.vue
│   ├── map/                 # 地图导航页面
│   │   └── map.vue
│   ├── scenic/              # 景点详情页面
│   │   └── detail.vue
│   ├── profile/             # 个人中心页面
│   │   └── footprints.vue
│   ├── quiz/                # AI科考页面
│   │   └── index.vue
│   └── vr/                  # VR体验页面
│       └── experience.vue
├── common/                  # 公共文件
│   ├── config.js           # 配置文件
│   ├── request.js          # 网络请求封装
│   ├── router.js           # 路由配置
│   └── operate.js          # 工具函数
├── api/                     # API接口
│   ├── user.js
│   └── config.js           # API密钥配置
├── static/                  # 本地静态资源
│   └── image/              # 本地图片（已迁移至OSS）
├── pages.json              # 页面配置
├── manifest.json           # 小程序配置
└── README.md              # 项目文档

## 🏛️ 衡阳六大文化景点

| 景点               | 类型     | 特色                   | 历史价值                 |
| ------------------ | -------- | ---------------------- | ------------------------ |
| **南岳衡山** | 道教名山 | 五岳之一，道教佛教圣地 | 始于秦汉，道教发源地之一 |
| **石鼓书院** | 千年学府 | 中国四大书院之一       | 始于唐代，千年文化传承   |
| **回雁峰**   | 衡阳地标 | 衡阳八景之首           | "北雁南飞，至此歇翅停回" |
| **南华大学** | 现代学府 | 现代教育文化地标       | 培育英才的知识殿堂       |
| **东洲岛**   | 江心绿洲 | 湘江中的文化岛屿       | 诗意盎然的文化名胜       |
| **岣嵝峰**   | 文化名山 | 传统建筑文化代表       | 古韵悠长的历史文化       |

## 🍜 衡阳六大特色美食

| 美食名称             | 类型     | 特色               | 风味描述           |
| -------------------- | -------- | ------------------ | ------------------ |
| **青椒剐骨肉** | 衡阳土菜 | 香辣、下饭神器     | 锅气十足，肉质鲜嫩 |
| **衡阳鱼粉**   | 早餐之光 | 鱼汤浓郁，鲜香     | 唤醒一天的城市味道 |
| **衡阳土头碗** | 宴席头牌 | 用料丰富，寓意美满 | 口感醇厚，家的味道 |
| **衡东脆肚**   | 衡东名菜 | 口感脆爽，酸辣开胃 | 湘菜的创新代表作   |
| **茶油炒土鸡** | 养生湘菜 | 茶油清香，滋补养生 | 肉质紧实，原汁原味 |
| **杨桥麸子肉** | 非遗美食 | 入口即化，肥而不腻 | 工艺复杂，百年传承 |

## 核心功能

### ✅ 已实现功能

- **🏠 首页轮播展示**

  - 六大景点3秒自动轮播
  - 古韵背景与视觉特效
  - 点击轮播图直接跳转地图定位
  - 新增实时天气徽章
  - 新增"我的印记"系统入口
- **✨ 个人足迹系统 (衡阳印记)**

  - 在景点详情页实现"盖章打卡"与"取消打卡"功能
  - 打卡记录持久化至本地缓存
  - 全新"衡阳星宿图"主题足迹展示页面
  - 动态展示已点亮的景点"星辰"和游玩进度
  - 添加入口至"AI智能科考"
- **🤖 AI智能科考**

  - 集成智谱GLM-4大模型，实现智能问答
  - "暗夜卷轴"主题聊天UI，营造沉浸式科考氛围
  - 根据答题结果，授予不同称号
- **🗺️ 智能地图导航**

  - 腾讯地图API集成
  - 六大景点精准标记定位
  - 实时景点信息卡片展示
  - 一键导航到目标景点
  - 地图图例和交互控制
- **📖 沉浸式景点详情**

  - 古代卷轴展开动画效果
  - 五卷章节式内容架构
  - 视差滚动和微交互动画
  - 图片对比滑块功能
  - 历史文化深度介绍
- **☁️ 配置化资源架构**

  - 抽离所有图片URL至 `common/config.js` 统一管理
  - 全项目重构，实现图片资源的动态加载
  - 极大提升项目可维护性，便于后续更换CDN或路径
- **📱 移动端体验优化**

  - 微信小程序完美适配
  - 流畅的触摸交互
  - 响应式设计
  - 古韵风格UI/UX设计

### 🚧 开发中功能

- **🥽 VR全景体验** - 360度景点虚拟游览
- **👤 用户系统** - 收藏、评价、分享功能
- **🔍 智能推荐** - 基于位置的个性化推荐
- **📊 数据统计** - 景点热度和用户行为分析

## 🚀 运行指南

### 环境要求

- HBuilderX 3.6+
- 微信开发者工具
- Node.js 16+

### 快速开始

1. **克隆项目**

   ```bash
   git clone <repository-url>
   cd yunVr
   ```
2. **安装依赖**

   ```bash
   npm install
   ```
3. **开发运行**

   - 使用HBuilderX打开项目
   - 选择"运行" → "运行到小程序模拟器" → "微信开发者工具"
   - 在微信开发者工具中预览效果
4. **真机测试**

   - 在微信开发者工具中点击"预览"
   - 使用微信扫码在手机上测试

## 🎨 设计理念

### 古韵风格设计

- **主色调**: 暖金色(#FFD700) + 古韵棕(#8B4513) + 典雅米白(#FFF8DC)
- **背景配色**: 深棕红(#2d1b1b) → 暖棕色(#4a3226) → 深棕色(#3d2817) 渐变
- **视觉效果**: 古代卷轴展开、视差滚动、温润渐变遮罩
- **交互设计**: 流畅微动画、缓动过渡、沉浸式文化体验
- **设计理念**: 传统文化质感与现代技术完美融合，温暖亲和

### 用户体验优化

- **流畅动画**: 60fps丝滑动画效果
- **快速加载**: 阿里云CDN加速 + 智能图片预加载
- **温暖视觉**: 暖棕古韵配色，长时间浏览更舒适
- **直观导航**: 智能面包屑导航 + 一键返回
- **响应式设计**: 完美适配各种屏幕尺寸和设备

## 📈 技术亮点

### 1. 云端架构设计

```javascript
// OSS图片路径示例
const imageBaseUrl = 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/'
const hengshanView = `${imageBaseUrl}hengshan/hengshanview.jpg`
```

### 2. 暖棕古韵配色实现

```scss
.detail-container {
  background: linear-gradient(180deg, #2d1b1b 0%, #4a3226 50%, #3d2817 100%);
}

.scroll-container {
  background: linear-gradient(180deg, transparent 0%, rgba(45, 27, 27, 0.95) 10%, rgba(61, 40, 23, 0.98) 20%);
}
```

### 3. 视差滚动实现

```scss
.scroll-section {
  transform: translateY(100rpx);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.section-active {
    opacity: 1;
    transform: translateY(0);
  }
}
```

### 4. 地图标记优化

```javascript
// 动态标记生成
markers: this.scenicSpots.map(spot => ({
  id: spot.id,
  latitude: spot.latitude,
  longitude: spot.longitude,
  iconPath: this.createCustomMarker(spot),
  callout: { /* 自定义样式 */ }
}))
```

## 📋 开发计划

### 近期计划 (Q1 2025)

- [ ] VR全景体验功能开发
- [ ] 用户收藏和评价系统
- [ ] 景点导航路线优化
- [ ] 多语言支持（英文版本）

### 中期计划 (Q2 2025)

- [ ] 后端API开发 (SpringBoot + MySQL)
- [ ] 用户数据持久化
- [ ] 智能推荐算法
- [ ] 社交分享功能

### 长期计划 (Q3-Q4 2025)

- [ ] AR增强现实体验
- [ ] 线下活动集成
- [ ] 旅游电商功能

## 📊 版本更新日志

### v3.0.0 (2025-06-28) 🚀

**核心功能扩展 - "足迹"与"AI科考"系统上线**

- ✨ **新功能**
  - **个人足迹系统 (衡阳印记):** 新增打卡、取消打卡功能；创建个人足迹地图，采用"星宿图"主题设计，展示游览进度。
  - **AI智能科考:** 新增AI问答模块；集成智谱GLM-4大模型，前端直连（测试阶段）；设计"暗夜卷轴"风格的聊天式UI；内置10道衡阳文化题目。
- 🔧 **架构重构**
  - **全局资源管理:** 新增 `common/config.js` 配置文件；重构所有页面，将硬编码的图片URL全部替换为从配置文件动态加载，提升项目可维护性。
- 🎨 **UI/UX优化**
  - **足迹页面:** 全新"衡阳星宿图"主题UI，替换原有网格布局，修复顶部标题遮挡问题。
  - **科考页面:** "暗夜卷轴"主题UI，优化交互流程，修复背景图404问题。
  - **首页:** 新增天气徽章、足迹入口，优化推荐栏UI。

### v2.1.0 (2025-06-28) 🎨

**UI/UX优化 - 暖棕古韵配色升级**

- 🎨 **视觉设计优化**

  - 全新暖棕古韵配色方案，告别冰冷黑色背景
  - 深棕红(#2d1b1b) → 暖棕色(#4a3226) → 深棕色(#3d2817)渐变
  - 优化卷轴容器和卡片背景，增强层次感
  - 统一导航栏配色，提升整体视觉协调性
- ✨ **用户体验提升**

  - 温暖亲和的视觉感受，更符合文化旅游主题
  - 长时间浏览更舒适，减少视觉疲劳
  - 增强古典文化质感，贴近传统建筑色调
  - 保持神秘古韵的同时增加温润感

### v2.0.0 (2025-06-28) 🎉

**重大更新 - 云端架构 + 沉浸式体验**

- ✨ **新功能**

  - 完成景点详情页面，沉浸式卷轴体验
  - 实现阿里云OSS图片存储架构
  - 添加视差滚动和古代卷轴动画效果
  - 集成图片对比滑块功能
  - 六大景点个性化数据内容完整展示
- 🚀 **性能优化**

  - 突破微信小程序2MB主包限制
  - CDN加速，图片加载速度提升300%
  - 优化动画性能，实现60fps流畅体验
  - 改进滚动激活机制，内容展示更流畅
- 🐛 **问题修复**

  - 修复地图返回按钮导航问题
  - 解决分包配置错误
  - 优化图片路径引用
  - 修复景点详情数据显示问题

### v1.2.0 (2025-06-28)

**地图导航功能**

- 新增地图导航功能，集成腾讯地图API
- 实现衡阳六大景点地图标记和定位
- 添加景点信息卡片展示
- 支持从首页轮播图直接跳转到对应景点位置

### v1.1.0 (2025-6-27)

**原生组件优化**

- 移除第三方UI组件库，解决小程序兼容性问题
- 采用原生组件 + 自定义样式方案
- 优化按钮设计，添加emoji图标

### v1.0.0 (2025-06-27)

**项目初始化**

- 完成首页基础框架
- 实现六大景点轮播图
- 设计古韵风格UI界面

---

**衡阳古韵** - 让传统文化在数字时代焕发新的生命力 🏛️✨
