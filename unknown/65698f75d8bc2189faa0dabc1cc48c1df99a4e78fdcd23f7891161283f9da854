{"version": 3, "file": "experience.js", "sources": ["pages/vr/experience.vue", "../../../../App/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdnIvZXhwZXJpZW5jZS52dWU"], "sourcesContent": ["<template>\n\t<view class=\"vr-container\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<view class=\"vr-navbar\">\n\t\t\t<view class=\"navbar-content\">\n\t\t\t\t<view class=\"back-btn\" @click=\"goBack\">\n\t\t\t\t\t<text class=\"back-icon\">←</text>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"navbar-title\">{{ scenicName }} VR体验</text>\n\t\t\t\t<view class=\"placeholder\"></view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- VR体验区域 -->\n\t\t<view class=\"vr-content\">\n\t\t\t<!-- 加载提示 -->\n\t\t\t<view class=\"loading-overlay\" v-if=\"isLoading\">\n\t\t\t\t<view class=\"loading-box\">\n\t\t\t\t\t<text class=\"loading-icon\">🥽</text>\n\t\t\t\t\t<text class=\"loading-text\">正在加载VR体验...</text>\n\t\t\t\t\t<text class=\"loading-desc\">请稍候，即将进入{{ scenicName }}的虚拟世界</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- VR Web-view -->\n\t\t\t<web-view \n\t\t\t\tv-if=\"vrUrl\" \n\t\t\t\t:src=\"vrUrl\" \n\t\t\t\t@message=\"onMessage\"\n\t\t\t\t@load=\"onLoad\"\n\t\t\t\t@error=\"onError\"\n\t\t\t></web-view>\n\t\t\t\n\t\t\t<!-- 错误提示 -->\n\t\t\t<view class=\"error-box\" v-if=\"hasError\">\n\t\t\t\t<text class=\"error-icon\">😔</text>\n\t\t\t\t<text class=\"error-title\">VR体验加载失败</text>\n\t\t\t\t<text class=\"error-desc\">请检查网络连接后重试</text>\n\t\t\t\t<view class=\"retry-btn\" @click=\"retryLoad\">\n\t\t\t\t\t<text class=\"btn-text\">重新加载</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- VR体验说明 -->\n\t\t<view class=\"vr-tips\" v-if=\"!isLoading && !hasError\">\n\t\t\t<view class=\"tips-content\">\n\t\t\t\t<text class=\"tips-icon\">💡</text>\n\t\t\t\t<text class=\"tips-text\">拖拽屏幕可360°观看，双指缩放调整视角</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tscenicId: null,\n\t\t\tscenicName: '',\n\t\t\tvrUrl: '',\n\t\t\tisLoading: true,\n\t\t\thasError: false,\n\t\t\t\n\t\t\t// VR链接映射\n\t\t\tvrLinks: {\n\t\t\t\t1: 'https://www.720yun.com/t/9dcjvzmkea7?scene_id=20012473',  // 南岳衡山\n\t\t\t\t2: 'https://www.720yun.com/t/38cjt7umvO2?scene_id=14943450',  // 石鼓书院\n\t\t\t\t3: 'https://www.720yun.com/t/899jv7yOuy3?scene_id=21330843',  // 回雁峰\n\t\t\t\t4: 'https://www.720yun.com/t/3a62cmz8cbn?scene_id=543271',    // 南华大学\n\t\t\t\t5: 'https://www.720yun.com/t/a4vki9ryg87?scene_id=34031063',  // 东洲岛\n\t\t\t\t6: 'https://www.720yun.com/t/25vkib1h729?scene_id=37482643'   // 岣嵝峰\n\t\t\t},\n\t\t\t\n\t\t\t// 景点名称映射\n\t\t\tscenicNames: {\n\t\t\t\t1: '南岳衡山',\n\t\t\t\t2: '石鼓书院', \n\t\t\t\t3: '回雁峰',\n\t\t\t\t4: '南华大学',\n\t\t\t\t5: '东洲岛',\n\t\t\t\t6: '岣嵝峰'\n\t\t\t}\n\t\t}\n\t},\n\t\n\tonLoad(options) {\n\t\tconsole.log('VR页面参数:', options)\n\t\t\n\t\t// 获取景点ID\n\t\tthis.scenicId = parseInt(options.scenicId) || 1\n\t\tthis.scenicName = this.scenicNames[this.scenicId] || '未知景点'\n\t\tthis.vrUrl = this.vrLinks[this.scenicId]\n\t\t\n\t\tif (!this.vrUrl) {\n\t\t\tthis.hasError = true\n\t\t\tthis.isLoading = false\n\t\t\treturn\n\t\t}\n\t\t\n\t\t// 设置页面标题\n\t\tuni.setNavigationBarTitle({\n\t\t\ttitle: `${this.scenicName} VR体验`\n\t\t})\n\t\t\n\t\tconsole.log('准备加载VR:', this.vrUrl)\n\t},\n\t\n\tmethods: {\n\t\t// Web-view加载完成\n\t\tonLoad() {\n\t\t\tconsole.log('VR页面加载完成')\n\t\t\tthis.isLoading = false\n\t\t\tthis.hasError = false\n\t\t},\n\t\t\n\t\t// Web-view加载错误\n\t\tonError(e) {\n\t\t\tconsole.error('VR页面加载错误:', e)\n\t\t\tthis.isLoading = false\n\t\t\tthis.hasError = true\n\t\t\t\n\t\t\tuni.showToast({\n\t\t\t\ttitle: 'VR体验加载失败',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t},\n\t\t\n\t\t// 接收Web-view消息\n\t\tonMessage(e) {\n\t\t\tconsole.log('收到VR页面消息:', e)\n\t\t},\n\t\t\n\t\t// 重新加载\n\t\tretryLoad() {\n\t\t\tthis.isLoading = true\n\t\t\tthis.hasError = false\n\t\t\t\n\t\t\t// 重新设置URL触发重新加载\n\t\t\tconst currentUrl = this.vrUrl\n\t\t\tthis.vrUrl = ''\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.vrUrl = currentUrl\n\t\t\t})\n\t\t},\n\t\t\n\t\t// 返回上一页\n\t\tgoBack() {\n\t\t\tuni.navigateBack({\n\t\t\t\tfail: () => {\n\t\t\t\t\t// 如果没有上一页，跳转到首页\n\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n.vr-container {\n\twidth: 100vw;\n\theight: 100vh;\n\tbackground: #000;\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.vr-navbar {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tz-index: 1000;\n\tbackground: rgba(0, 0, 0, 0.8);\n\tbackdrop-filter: blur(10rpx);\n\t\n\t.navbar-content {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 20rpx 30rpx;\n\t\tpadding-top: calc(20rpx + var(--status-bar-height, 0));\n\t\t\n\t\t.back-btn {\n\t\t\twidth: 60rpx;\n\t\t\theight: 60rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tbackground: rgba(255, 255, 255, 0.2);\n\t\t\tborder-radius: 50%;\n\t\t\t\n\t\t\t.back-icon {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tcolor: #FFF;\n\t\t\t\tfont-weight: bold;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.navbar-title {\n\t\t\tfont-size: 32rpx;\n\t\t\tcolor: #FFD700;\n\t\t\tfont-weight: bold;\n\t\t\ttext-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.8);\n\t\t}\n\t\t\n\t\t.placeholder {\n\t\t\twidth: 60rpx;\n\t\t}\n\t}\n}\n\n.vr-content {\n\twidth: 100%;\n\theight: 100%;\n\tposition: relative;\n}\n\n.loading-overlay {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tz-index: 999;\n\t\n\t.loading-box {\n\t\ttext-align: center;\n\t\t\n\t\t.loading-icon {\n\t\t\tfont-size: 120rpx;\n\t\t\tdisplay: block;\n\t\t\tmargin-bottom: 40rpx;\n\t\t\tanimation: pulse 2s infinite;\n\t\t}\n\t\t\n\t\t.loading-text {\n\t\t\tfont-size: 36rpx;\n\t\t\tcolor: #FFD700;\n\t\t\tfont-weight: bold;\n\t\t\tdisplay: block;\n\t\t\tmargin-bottom: 20rpx;\n\t\t}\n\t\t\n\t\t.loading-desc {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #CCC;\n\t\t\tdisplay: block;\n\t\t}\n\t}\n}\n\n.error-box {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\ttext-align: center;\n\tpadding: 60rpx;\n\t\n\t.error-icon {\n\t\tfont-size: 120rpx;\n\t\tdisplay: block;\n\t\tmargin-bottom: 40rpx;\n\t}\n\t\n\t.error-title {\n\t\tfont-size: 36rpx;\n\t\tcolor: #FF6B6B;\n\t\tfont-weight: bold;\n\t\tdisplay: block;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.error-desc {\n\t\tfont-size: 28rpx;\n\t\tcolor: #CCC;\n\t\tdisplay: block;\n\t\tmargin-bottom: 40rpx;\n\t}\n\t\n\t.retry-btn {\n\t\tbackground: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);\n\t\tpadding: 20rpx 40rpx;\n\t\tborder-radius: 40rpx;\n\t\tdisplay: inline-block;\n\t\t\n\t\t.btn-text {\n\t\t\tcolor: #FFF;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: bold;\n\t\t}\n\t}\n}\n\n.vr-tips {\n\tposition: fixed;\n\tbottom: 30rpx;\n\tleft: 30rpx;\n\tright: 30rpx;\n\tz-index: 998;\n\t\n\t.tips-content {\n\t\tbackground: rgba(0, 0, 0, 0.7);\n\t\tpadding: 20rpx 30rpx;\n\t\tborder-radius: 40rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackdrop-filter: blur(10rpx);\n\t\t\n\t\t.tips-icon {\n\t\t\tfont-size: 28rpx;\n\t\t\tmargin-right: 15rpx;\n\t\t}\n\t\t\n\t\t.tips-text {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #FFF;\n\t\t}\n\t}\n}\n\n@keyframes pulse {\n\t0%, 100% { transform: scale(1); }\n\t50% { transform: scale(1.1); }\n}\n</style>\n", "import MiniProgramPage from 'D:/Users/<USER>/Desktop/yunVr/pages/vr/experience.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAuDA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA;AAAA,MAGV,SAAS;AAAA,QACR,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,MACH;AAAA;AAAA,MAGD,aAAa;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACJ;AAAA,IACD;AAAA,EACA;AAAA,EAED,OAAO,SAAS;AACfA,kBAAAA,oDAAY,WAAW,OAAO;AAG9B,SAAK,WAAW,SAAS,QAAQ,QAAQ,KAAK;AAC9C,SAAK,aAAa,KAAK,YAAY,KAAK,QAAQ,KAAK;AACrD,SAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ;AAEvC,QAAI,CAAC,KAAK,OAAO;AAChB,WAAK,WAAW;AAChB,WAAK,YAAY;AACjB;AAAA,IACD;AAGAA,kBAAAA,MAAI,sBAAsB;AAAA,MACzB,OAAO,GAAG,KAAK,UAAU;AAAA,KACzB;AAEDA,uEAAY,WAAW,KAAK,KAAK;AAAA,EACjC;AAAA,EAED,SAAS;AAAA;AAAA,IAER,SAAS;AACRA,oBAAAA,MAAY,MAAA,OAAA,kCAAA,UAAU;AACtB,WAAK,YAAY;AACjB,WAAK,WAAW;AAAA,IAChB;AAAA;AAAA,IAGD,QAAQ,GAAG;AACVA,oBAAAA,MAAA,MAAA,SAAA,kCAAc,aAAa,CAAC;AAC5B,WAAK,YAAY;AACjB,WAAK,WAAW;AAEhBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,OACN;AAAA,IACD;AAAA;AAAA,IAGD,UAAU,GAAG;AACZA,oBAAAA,MAAA,MAAA,OAAA,kCAAY,aAAa,CAAC;AAAA,IAC1B;AAAA;AAAA,IAGD,YAAY;AACX,WAAK,YAAY;AACjB,WAAK,WAAW;AAGhB,YAAM,aAAa,KAAK;AACxB,WAAK,QAAQ;AACb,WAAK,UAAU,MAAM;AACpB,aAAK,QAAQ;AAAA,OACb;AAAA,IACD;AAAA;AAAA,IAGD,SAAS;AACRA,oBAAAA,MAAI,aAAa;AAAA,QAChB,MAAM,MAAM;AAEXA,wBAAAA,MAAI,SAAS;AAAA,YACZ,KAAK;AAAA,WACL;AAAA,QACF;AAAA,OACA;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;AC7JA,GAAG,WAAW,eAAe;"}