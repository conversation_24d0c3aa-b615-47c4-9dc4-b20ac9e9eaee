"use strict";
const common_vendor = require("../../common/vendor.js");
const api_config = require("../../api/config.js");
const common_config = require("../../common/config.js");
const SYSTEM_PROMPT_TEMPLATE = (spotName) => `你是一位热情、博学、风趣的衡阳本地导游"衡阳通"。你的任务是：
1.  **角色扮演**: 以"衡阳通"的身份，用自然、欢迎的语气与用户对话。
2.  **开场白**: 你的第一句话必须是："欢迎来到${spotName}！这里是衡阳的瑰宝之一。除了眼前的美景，你还想了解衡阳的哪些风土人情呢？无论是嗦螺的来历，还是王船山的逸闻，我都可以为你娓娓道来。"
3.  **知识范围**: 你是衡阳的万事通。你的知识不限于我给你提供的这个初始景点。你可以自由介绍衡阳的任何方面，包括但不限于：地方美食（如鱼粉、唆螺）、历史名人（如蔡伦、王夫之）、方言文化、未收录的小众景点、民俗节庆等。
4.  **互动风格**: 主动引导对话，激发用户的好奇心。当用户的问题超出你的知识范围时，要礼貌地承认并尝试从其他角度提供相关信息。
5.  **核心要求**: 保持对话的趣味性和互动性，让用户感觉在和一位真实的衡阳朋友聊天。`;
const _sfc_main = {
  data() {
    return {
      featuredSpot: {},
      messages: [],
      userInput: "",
      isLoading: false,
      scrollTop: 0
    };
  },
  computed: {
    chatHistory() {
      return this.messages.filter((m) => m.role !== "system");
    }
  },
  onLoad(options) {
    this.loadFeaturedSpot(options.id);
    this.startChat();
  },
  methods: {
    loadFeaturedSpot(id) {
      const allSpots = {
        1: { id: 1, name: "南岳衡山", subtitle: "五岳独秀，禅宗圣地", folder: "hengshan" },
        2: { id: 2, name: "石鼓书院", subtitle: "千年学府，理学源头", folder: "shigu" },
        3: { id: 3, name: "回雁峰", subtitle: "雁城之巅，俯瞰湘江", folder: "huiyan" },
        4: { id: 4, name: "南华大学", subtitle: "核学府，育人才", folder: "nanhua" },
        5: { id: 5, name: "东洲岛", subtitle: "江心绿洲，城市氧吧", folder: "dongzhou" },
        6: { id: 6, name: "岣嵝峰", subtitle: "神秘禹碑，千古之谜", folder: "goulou" }
      };
      const spotData = allSpots[id] || allSpots[1];
      this.featuredSpot = {
        ...spotData,
        image: common_config.SCENIC_IMAGES[spotData.folder].view
      };
    },
    startChat() {
      this.messages = [
        { role: "system", content: SYSTEM_PROMPT_TEMPLATE(this.featuredSpot.name) },
        { role: "assistant", content: `欢迎来到${this.featuredSpot.name}！这里是衡阳的瑰宝之一。除了眼前的美景，你还想了解衡阳的哪些风土人情呢？无论是嗦螺的来历，还是王船山的逸闻，我都可以为你娓娓道来。` }
      ];
    },
    sendMessage() {
      if (!this.userInput.trim() || this.isLoading)
        return;
      const userMessage = { role: "user", content: this.userInput.trim() };
      this.messages = [...this.messages, userMessage];
      this.userInput = "";
      this.fetchAIResponse();
    },
    async fetchAIResponse() {
      this.isLoading = true;
      this.scrollToBottom();
      const apiMessages = this.messages.filter((msg) => msg.role !== "system");
      apiMessages.unshift({ role: "system", content: SYSTEM_PROMPT_TEMPLATE(this.featuredSpot.name) });
      try {
        const res = await common_vendor.index.request({
          url: "https://open.bigmodel.cn/api/paas/v4/chat/completions",
          method: "POST",
          header: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${api_config.config.ZHIPU_API_KEY}`
          },
          data: {
            model: "glm-4",
            messages: apiMessages,
            stream: false
          }
        });
        if (res.statusCode === 200 && res.data.choices) {
          this.messages.push(res.data.choices[0].message);
        } else {
          this.handleError(res.data);
        }
      } catch (err) {
        this.handleError(err);
      } finally {
        this.isLoading = false;
        this.scrollToBottom();
      }
    },
    handleError(error) {
      common_vendor.index.__f__("error", "at pages/discover/index.vue:153", "AI请求失败:", error);
      this.messages.push({
        role: "assistant",
        content: "抱歉，导览线路有些繁忙，请稍后再试。"
      });
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    goToDetail() {
      common_vendor.index.navigateTo({
        url: `/pages/scenic/detail?id=${this.featuredSpot.id}`
      });
    },
    goToVR() {
      common_vendor.index.navigateTo({
        url: `/pages/vr/experience?scenicId=${this.featuredSpot.id}`
      });
    },
    scrollToBottom() {
      this.$nextTick(() => {
        this.scrollTop = this.messages.length * 1e3;
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.featuredSpot.image
  }, $data.featuredSpot.image ? common_vendor.e({
    b: `url(${$data.featuredSpot.image})`,
    c: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    d: common_vendor.t($data.featuredSpot.name),
    e: common_vendor.t($data.featuredSpot.subtitle),
    f: common_vendor.o((...args) => $options.goToDetail && $options.goToDetail(...args)),
    g: common_vendor.o((...args) => $options.goToVR && $options.goToVR(...args)),
    h: `url(${$data.featuredSpot.image})`,
    i: $data.featuredSpot.id === 2 ? "center 20%" : "center",
    j: common_vendor.f($options.chatHistory, (message, index, i0) => {
      return {
        a: common_vendor.t(message.role === "assistant" ? "通" : "我"),
        b: common_vendor.n(message.role),
        c: common_vendor.t(message.content),
        d: index,
        e: common_vendor.n(message.role)
      };
    }),
    k: $data.isLoading
  }, $data.isLoading ? {} : {}, {
    l: $data.scrollTop,
    m: $data.isLoading,
    n: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    o: $data.userInput,
    p: common_vendor.o(($event) => $data.userInput = $event.detail.value),
    q: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    r: $data.isLoading || !$data.userInput
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/discover/index.js.map
