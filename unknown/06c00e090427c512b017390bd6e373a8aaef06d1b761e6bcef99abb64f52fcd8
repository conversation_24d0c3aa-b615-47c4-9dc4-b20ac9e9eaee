{"version": 3, "file": "index.js", "sources": ["pages/quiz/index.vue", "../../../../App/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcXVpei9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"quiz-container ancient-scroll\">\r\n\t\t<scroll-view :scroll-y=\"true\" class=\"chat-history\" :scroll-top=\"scrollTop\">\r\n\t\t\t<!-- 历史消息 -->\r\n\t\t\t<view v-for=\"(message, index) in chatHistory\" :key=\"index\" class=\"message-wrapper\" :class=\"[message.role]\">\r\n\t\t\t\t<view class=\"avatar seal\" :class=\"[message.role]\">{{ message.role === 'assistant' ? '官' : '生' }}</view>\r\n\t\t\t\t<view class=\"message-content ink-blot\">\r\n\t\t\t\t\t<text :user-select=\"true\">{{ message.content }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 加载状态 -->\r\n\t\t\t<view v-if=\"isLoading\" class=\"message-wrapper assistant\">\r\n\t\t\t\t<view class=\"avatar seal assistant\">官</view>\r\n\t\t\t\t<view class=\"message-content ink-blot loading\">\r\n\t\t\t\t\t<view class=\"dot-flashing\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\r\n\t\t<view class=\"input-area inkstone\">\r\n\t\t\t<input v-model=\"userInput\" type=\"text\" placeholder=\"请挥毫...\" class=\"input-field\" :disabled=\"isLoading\" @confirm=\"sendMessage\" />\r\n\t\t\t<button @click=\"sendMessage\" class=\"send-button seal-stamp\" :disabled=\"isLoading || !userInput\">\r\n\t\t\t\t<text>提交</text>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport config from '@/api/config.js';\r\n\r\nconst SYSTEM_PROMPT = `你是一位学识渊博、风趣幽默的衡阳历史文化主考官。你的任务分为两个阶段：“常规科考”和“殿试”。\r\n\r\n**第一阶段：常规科考**\r\n\r\n你的知识库是以下JSON数组，你必须严格依赖它来出题和判断。\r\n知识库:\r\n[\r\n    {\"id\": 1, \"question\": \"“雁阵惊寒，声断衡阳之浦”出自哪位唐代著名诗人的哪篇作品？\", \"answer\": \"王勃的《滕王阁序》\", \"analysis\": \"这句名句出自初唐四杰之一王勃的《滕王阁序》，描绘了衡阳回雁峰的典故，意境高远。\"},\r\n    {\"id\": 2, \"question\": \"中国古代四大书院中，位于衡阳的是哪一座？\", \"answer\": \"石鼓书院\", \"analysis\": \"石鼓书院位于衡阳市石鼓山，是湖湘文化的重要发源地，始建于唐元和五年（810年），乃中国古代四大书院之首。\"},\r\n    {\"id\": 3, \"question\": \"南岳衡山的最高峰叫什么名字？\", \"answer\": \"祝融峰\", \"analysis\": \"祝融峰海拔1300.2米，是衡山的最高峰，相传是火神祝融的栖息地，峰顶的祝融殿更是观日出、赏云海的绝佳之地。\"},\r\n    {\"id\": 4, \"question\": \"位于衡阳市区的“东洲岛”，因哪位晚清重臣在此建立“船山书院”而闻名？\", \"answer\": \"彭玉麟\", \"analysis\": \"晚清重臣彭玉麟于1883年在东洲岛上重建船山书院，以纪念明末清初的大思想家王夫之（号船山），使得东洲岛文脉得以传承。\"},\r\n    {\"id\": 5, \"question\": \"“纸上得来终觉浅，绝知此事要躬行”这句千古名句，是诗人在何地有感而发？尽管诗人并非衡阳人，但这句诗蕴含的精神与衡阳哪所书院的学风高度契合？\", \"answer\": \"陆游，石鼓书院\", \"analysis\": \"这句诗出自南宋诗人陆游的《冬夜读书示子聿》。它所倡导的“知行合一”的治学精神，与石鼓书院注重实践、经世致用的学风不谋而合。\"},\r\n    {\"id\": 6, \"question\": \"耒阳，古称“蔡伦故里”，是纪念哪位对世界文明有重大贡献的东汉发明家？\", \"answer\": \"蔡伦\", \"analysis\": \"正是蔡伦改进了造纸术，极大地推动了人类文化的传播与发展。耒阳至今仍保留着蔡伦纪念园，以纪念这位伟大的发明家。\"},\r\n    {\"id\": 7, \"question\": \"明末清初的伟大思想家，世称“船山先生”的是哪位衡阳先贤？\", \"answer\": \"王夫之\", \"analysis\": \"王夫之是与顾炎武、黄宗羲并称的明末三大儒。他的“船山思想”是中国古代朴素唯物论的结晶，对后世影响深远。\"},\r\n    {\"id\": 8, \"question\": \"衡阳有一道著名的风味小吃，需要用嘴“唆”着吃，因此得名，请问是什么？\", \"answer\": \"衡阳唆螺\", \"analysis\": \"衡阳唆螺是极具地方特色的风味小吃，以紫苏、大蒜、辣椒等佐料爆炒而成，香辣鲜美，是衡阳夜市文化的代表。\"},\r\n    {\"id\": 9, \"question\": \"衡阳的市花是什么？它象征着什么样的城市品格？\", \"answer\": \"月季花\", \"analysis\": \"衡阳市花是月季花，它象征着衡阳人民热情奔放、坚韧不拔的品格。它四季常开，也寓意着衡阳的生机与活力。\"},\r\n    {\"id\": 10, \"question\": \"“天下南岳”之称指的是衡山，但广义上的衡山七十二峰，其首峰和尾峰分别在哪里？\", \"answer\": \"首峰在长沙岳麓山，尾峰在衡阳回雁峰\", \"analysis\": \"这是一个有趣的地理文化知识。广义上的南岳七十二峰，起于长沙岳麓山云麓峰，止于衡阳城南回雁峰。所以古人有云“回雁为首，岳麓为足”。\"}\r\n]\r\n\r\n你的行动规则:\r\n1.  **开场**: 当用户说“准备好了”或类似的话时，你必须以“好！常规科考现在开始。请听第一题：”开头，然后直接提出知识库中的第1个问题。\r\n2.  **出题与评判**: 在常规科考阶段，严格按照知识库顺序，一次只问一道题。用户回答后，根据'answer'判断对错，然后结合'analysis'给出风趣的点评。\r\n3.  **衔接**: 点评后，立即提出下一题。例如：“答得不错！接下来，请听第二题：...”。\r\n4.  **常规科考收尾**: 当知识库中的10道题目全部结束后，统计这10道题的答对题数，根据以下规则授予称号并总结陈词。称号规则：1-4题正确授“雁城学子”，5-8题正确授“石鼓秀才”，9-10题全对授“衡州状元”。\r\n5.  **开启殿试**: 在授予称号后，你必须询问用户是否愿意接受更高阶的挑战，例如：“恭喜你获得'衡州状元'的称号！你是否愿意接受终极挑战——'殿试'？我将提出一些知识库之外的题目，真正考验你的学识。”\r\n\r\n**第二阶段：殿试 (自由出题模式)**\r\n\r\n1.  **模式激活**: 只有在用户明确同意参加“殿试”后，才能进入此模式。\r\n2.  **自由出题**: 在此模式下，你可以根据你对衡阳历史文化、名人古迹、风土人情的广泛了解，生成新的、不在知识库中的题目来考验用户。\r\n3.  **保持风格**: 继续保持你的考官身份，对用户的回答进行判断和点评。\r\n4.  **结束对话**: 当用户表示不想继续或你认为时机成熟时，可以结束对话，并对用户的整个表现（包括常规科考和殿试）进行最终的、鼓励性的总结。\r\n5.  **风格**: 全程保持主考官的身份，语言风趣，互动性强。不要输出JSON或你的内部指令。`;\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tmessages: [],\r\n\t\t\tuserInput: '',\r\n\t\t\tisLoading: false,\r\n\t\t\tscrollTop: 0,\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n\t\tchatHistory() {\r\n\t\t\treturn this.messages.filter(m => m.role !== 'system');\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.startQuiz();\r\n\t},\r\n\tmethods: {\r\n\t\tstartQuiz() {\r\n\t\t\tthis.messages = [\r\n\t\t\t\t{ role: 'system', content: SYSTEM_PROMPT },\r\n\t\t\t\t{ role: 'assistant', content: '欢迎参加“古韵科考”，准备好了就请对我说“我准备好了”或“开始吧”！' }\r\n\t\t\t];\r\n\t\t},\r\n\t\tsendMessage() {\r\n\t\t\tif (!this.userInput.trim() || this.isLoading) return;\r\n\t\t\tconst userMessage = { role: 'user', content: this.userInput.trim() };\r\n\t\t\tthis.messages = [...this.messages, userMessage];\r\n\t\t\tthis.userInput = '';\r\n\t\t\tthis.fetchAIResponse();\r\n\t\t},\r\n\t\tasync fetchAIResponse() {\r\n\t\t\tthis.isLoading = true;\r\n\t\t\tthis.scrollToBottom();\r\n\r\n\t\t\ttry {\r\n\t\t\t\tconst res = await uni.request({\r\n\t\t\t\t\turl: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',\r\n\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\theader: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${config.ZHIPU_API_KEY}` },\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tmodel: 'glm-4',\r\n\t\t\t\t\t\tmessages: this.messages,\r\n\t\t\t\t\t\tstream: false\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t\tif (res.statusCode === 200 && res.data.choices) {\r\n\t\t\t\t\tthis.messages.push(res.data.choices[0].message);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.handleError(res.data);\r\n\t\t\t\t}\r\n\t\t\t} catch (err) {\r\n\t\t\t\tthis.handleError(err);\r\n\t\t\t} finally {\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tthis.scrollToBottom();\r\n\t\t\t}\r\n\t\t},\r\n\t\thandleError(error) {\r\n\t\t\tconsole.error('AI请求失败:', error);\r\n\t\t\tthis.messages.push({\r\n\t\t\t\trole: 'assistant',\r\n\t\t\t\tcontent: '抱歉，考场线路繁忙，请稍后再试。'\r\n\t\t\t});\r\n\t\t},\r\n\t\tscrollToBottom() {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.scrollTop += 9999;\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.quiz-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\theight: 100vh;\r\n\t\tbackground-color: #2c2828;\r\n\t\tbackground-image: url('/static/texture/dark_texture.png');\r\n\t\tposition: relative;\r\n\r\n\t\t&::before,\r\n\t\t&::after {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tbottom: 0;\r\n\t\t\twidth: 30rpx;\r\n\t\t\tbackground-color: #1a1616;\r\n\t\t\tbackground-image: linear-gradient(to right, rgba(0, 0, 0, 0.4), transparent, rgba(0, 0, 0, 0.4));\r\n\t\t\tbox-shadow: 0 0 15rpx rgba(0, 0, 0, 0.8);\r\n\t\t\tz-index: 1;\r\n\t\t}\r\n\r\n\t\t&::before {\r\n\t\t\tleft: 0;\r\n\t\t}\r\n\r\n\t\t&::after {\r\n\t\t\tright: 0;\r\n\t\t}\r\n\t}\r\n\r\n\t.chat-history {\r\n\t\tflex: 1;\r\n\t\tpadding: 20rpx 50rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\toverflow-y: auto;\r\n\t}\r\n\r\n\t.message-wrapper {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 40rpx;\r\n\t\tmax-width: 85%;\r\n\t\talign-items: flex-start;\r\n\r\n\t\t&.user {\r\n\t\t\tflex-direction: row-reverse;\r\n\t\t\tmargin-left: auto;\r\n\t\t}\r\n\r\n\t\t&.assistant {\r\n\t\t\tflex-direction: row;\r\n\t\t\tmargin-right: auto;\r\n\t\t}\r\n\r\n\t\t.avatar.seal {\r\n\t\t\twidth: 80rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tflex-shrink: 0;\r\n\t\t\tfont-family: 'KaiTi', 'STKaiti', serif;\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\tborder: 4rpx solid;\r\n\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t&.user {\r\n\t\t\t\tbackground-color: #a98e72;\r\n\t\t\t\tcolor: #1a1616;\r\n\t\t\t\tborder-color: #d4b899;\r\n\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\tbox-shadow: 0 0 10rpx rgba(212, 184, 153, 0.3);\r\n\t\t\t}\r\n\r\n\t\t\t&.assistant {\r\n\t\t\t\tbackground-color: #4a423b;\r\n\t\t\t\tcolor: #d4b899;\r\n\t\t\t\tborder-color: #6a5f55;\r\n\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\tbox-shadow: 0 0 12rpx rgba(212, 184, 153, 0.5);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.message-content.ink-blot {\r\n\t\t\tpadding: 25rpx 35rpx;\r\n\t\t\tborder-radius: 20rpx 20rpx 20rpx 5rpx;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tline-height: 1.7;\r\n\t\t\tfont-family: 'KaiTi', 'STKaiti', serif;\r\n\t\t\tposition: relative;\r\n\t\t\tcolor: #e0e0e0;\r\n\r\n\t\t\t&.loading {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tmin-height: 50rpx;\r\n\t\t\t\tpadding: 25rpx 45rpx;\r\n\r\n\t\t\t\t.dot-flashing {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\twidth: 10rpx;\r\n\t\t\t\t\theight: 10rpx;\r\n\t\t\t\t\tborder-radius: 5rpx;\r\n\t\t\t\t\tbackground-color: #e0e0e0;\r\n\t\t\t\t\tcolor: #e0e0e0;\r\n\t\t\t\t\tanimation: dotFlashing 1s infinite linear alternate;\r\n\t\t\t\t\tanimation-delay: .5s;\r\n\r\n\t\t\t\t\t&::before,\r\n\t\t\t\t\t&::after {\r\n\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&::before {\r\n\t\t\t\t\t\tleft: -20rpx;\r\n\t\t\t\t\t\twidth: 10rpx;\r\n\t\t\t\t\t\theight: 10rpx;\r\n\t\t\t\t\t\tborder-radius: 5rpx;\r\n\t\t\t\t\t\tbackground-color: #e0e0e0;\r\n\t\t\t\t\t\tcolor: #e0e0e0;\r\n\t\t\t\t\t\tanimation: dotFlashing 1s infinite alternate;\r\n\t\t\t\t\t\tanimation-delay: 0s;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&::after {\r\n\t\t\t\t\t\tleft: 20rpx;\r\n\t\t\t\t\t\twidth: 10rpx;\r\n\t\t\t\t\t\theight: 10rpx;\r\n\t\t\t\t\t\tborder-radius: 5rpx;\r\n\t\t\t\t\t\tbackground-color: #e0e0e0;\r\n\t\t\t\t\t\tcolor: #e0e0e0;\r\n\t\t\t\t\t\tanimation: dotFlashing 1s infinite alternate;\r\n\t\t\t\t\t\tanimation-delay: 1s;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&.user .message-content.ink-blot {\r\n\t\t\tbackground-color: rgba(60, 55, 55, 0.8);\r\n\t\t\tborder-radius: 20rpx 20rpx 5rpx 20rpx;\r\n\t\t}\r\n\r\n\t\t&.assistant .message-content.ink-blot {\r\n\t\t\tbackground-color: rgba(30, 26, 26, 0.8);\r\n\t\t}\r\n\t}\r\n\r\n\t.input-area.inkstone {\r\n\t\tdisplay: flex;\r\n\t\tpadding: 20rpx 50rpx;\r\n\t\tbackground-color: #1a1616;\r\n\t\tborder-top: 2rpx solid #4a423b;\r\n\t\tbox-shadow: 0 -5rpx 15rpx rgba(0, 0, 0, 0.5);\r\n\t\talign-items: center;\r\n\t\tz-index: 2;\r\n\t}\r\n\r\n\t.input-field {\r\n\t\tflex: 1;\r\n\t\theight: 80rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t\tborder: none;\r\n\t\tbackground-color: #2c2828;\r\n\t\tborder-radius: 12rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\tcolor: #e0e0e0;\r\n\t\tfont-family: 'KaiTi', 'STKaiti', serif;\r\n\t}\r\n\r\n\t.send-button.seal-stamp {\r\n\t\twidth: 120rpx;\r\n\t\theight: 80rpx;\r\n\t\tpadding: 0;\r\n\t\tmargin: 0;\r\n\t\tbackground-color: #a98e72;\r\n\t\tcolor: #1a1616;\r\n\t\tborder-radius: 12rpx;\r\n\t\tborder: 4rpx solid #d4b899;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-family: 'KaiTi', 'STKaiti', serif;\r\n\t\tbox-shadow: 3rpx 3rpx 5rpx rgba(0, 0, 0, 0.3);\r\n\r\n\t\t&[disabled] {\r\n\t\t\tbackground-color: #555;\r\n\t\t\tborder-color: #777;\r\n\t\t\topacity: 0.7;\r\n\t\t\tcolor: #999;\r\n\t\t}\r\n\r\n\t\t&::after {\r\n\t\t\tborder: none;\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes dotFlashing {\r\n\t\t0% {\r\n\t\t\tbackground-color: #e0e0e0;\r\n\t\t}\r\n\r\n\t\t50%,\r\n\t\t100% {\r\n\t\t\tbackground-color: #666;\r\n\t\t}\r\n\t}\r\n</style> ", "import MiniProgramPage from 'D:/Users/<USER>/Desktop/yunVr/pages/quiz/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "config"], "mappings": ";;;AA+BA,MAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkCtB,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU,CAAE;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA;EAEZ;AAAA,EACD,UAAU;AAAA,IACT,cAAc;AACb,aAAO,KAAK,SAAS,OAAO,OAAK,EAAE,SAAS,QAAQ;AAAA,IACrD;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,UAAS;AAAA,EACd;AAAA,EACD,SAAS;AAAA,IACR,YAAY;AACX,WAAK,WAAW;AAAA,QACf,EAAE,MAAM,UAAU,SAAS,cAAe;AAAA,QAC1C,EAAE,MAAM,aAAa,SAAS,qCAAqC;AAAA;IAEpE;AAAA,IACD,cAAc;AACb,UAAI,CAAC,KAAK,UAAU,KAAI,KAAM,KAAK;AAAW;AAC9C,YAAM,cAAc,EAAE,MAAM,QAAQ,SAAS,KAAK,UAAU,KAAI;AAChE,WAAK,WAAW,CAAC,GAAG,KAAK,UAAU,WAAW;AAC9C,WAAK,YAAY;AACjB,WAAK,gBAAe;AAAA,IACpB;AAAA,IACD,MAAM,kBAAkB;AACvB,WAAK,YAAY;AACjB,WAAK,eAAc;AAEnB,UAAI;AACH,cAAM,MAAM,MAAMA,cAAG,MAAC,QAAQ;AAAA,UAC7B,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,QAAQ,EAAE,gBAAgB,oBAAoB,iBAAiB,UAAUC,WAAAA,OAAO,aAAa,GAAI;AAAA,UACjG,MAAM;AAAA,YACL,OAAO;AAAA,YACP,UAAU,KAAK;AAAA,YACf,QAAQ;AAAA,UACT;AAAA,QACD,CAAC;AAED,YAAI,IAAI,eAAe,OAAO,IAAI,KAAK,SAAS;AAC/C,eAAK,SAAS,KAAK,IAAI,KAAK,QAAQ,CAAC,EAAE,OAAO;AAAA,eACxC;AACN,eAAK,YAAY,IAAI,IAAI;AAAA,QAC1B;AAAA,MACC,SAAO,KAAK;AACb,aAAK,YAAY,GAAG;AAAA,MACrB,UAAU;AACT,aAAK,YAAY;AACjB,aAAK,eAAc;AAAA,MACpB;AAAA,IACA;AAAA,IACD,YAAY,OAAO;AAClBD,oBAAc,MAAA,MAAA,SAAA,+BAAA,WAAW,KAAK;AAC9B,WAAK,SAAS,KAAK;AAAA,QAClB,MAAM;AAAA,QACN,SAAS;AAAA,MACV,CAAC;AAAA,IACD;AAAA,IACD,iBAAiB;AAChB,WAAK,UAAU,MAAM;AACpB,aAAK,aAAa;AAAA,MACnB,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;ACxIA,GAAG,WAAW,eAAe;"}