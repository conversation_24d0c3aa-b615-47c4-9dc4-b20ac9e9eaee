{"version": 3, "file": "footprints.js", "sources": ["pages/profile/footprints.vue", "../../../../App/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJvZmlsZS9mb290cHJpbnRzLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"star-chart-container\">\r\n\t\t<view class=\"back-button\" @click=\"goHome\">\r\n\t\t\t<text class=\"back-icon\">‹</text>\r\n\t\t</view>\r\n\t\t<view class=\"header\">\r\n\t\t\t<text class=\"title\">我的衡阳印记</text>\r\n\t\t\t<text class=\"progress-text\">已点亮 {{ checkedInCount }} / {{ totalSpots }} 颗星辰</text>\r\n\t\t\t<progress :percent=\"progressPercent\" stroke-width=\"4\" activeColor=\"rgba(255, 215, 0, 0.8)\" backgroundColor=\"rgba(255, 255, 255, 0.1)\" class=\"progress-bar\"></progress>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"star-map\">\r\n\t\t\t<view \r\n\t\t\t\tv-for=\"item in allItems\" \r\n\t\t\t\t:key=\"item.key\" \r\n\t\t\t\tclass=\"grid-item\" \r\n\t\t\t\t:class=\"[item.type, { 'unlocked': item.isCheckedIn }]\"\r\n\t\t\t\t@click=\"goToDetail(item)\"\r\n\t\t\t>\r\n\t\t\t\t<image :src=\"item.image\" class=\"item-image\" mode=\"aspectFill\"></image>\r\n\t\t\t\t<view class=\"item-overlay\"></view>\r\n\t\t\t\t<view v-if=\"!item.isCheckedIn\" class=\"lock-icon\">\r\n\t\t\t\t\t<text>{{ item.type === 'scenic' ? '未点亮' : '未品尝' }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"item-name\">{{ item.name }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"action-button-container\">\r\n\t\t\t<button class=\"portal-button\" @click=\"goToQuiz\">\r\n\t\t\t\t<text>开启科考</text>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { SCENIC_IMAGES, FOOD_IMAGES } from '@/common/config.js';\r\nimport { database } from '@/common/database.js';\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tallItems: [],\r\n\t\t\tfootprints: {},\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n\t\tcheckedInCount() {\r\n\t\t\treturn this.allItems.filter(s => s.isCheckedIn).length;\r\n\t\t},\r\n\t\ttotalSpots() {\r\n\t\t\treturn this.allItems.length;\r\n\t\t},\r\n\t\tprogressPercent() {\r\n\t\t\treturn this.totalSpots > 0 ? (this.checkedInCount / this.totalSpots) * 100 : 0;\r\n\t\t}\r\n\t},\r\n\tonShow() {\r\n\t\tthis.loadData();\r\n\t},\r\n\tmethods: {\r\n\t\tloadData() {\r\n\t\t\tconst scenicSpots = Object.values(database.scenic).map(spot => ({\r\n\t\t\t\t...spot,\r\n\t\t\t\ttype: 'scenic',\r\n\t\t\t\tkey: `scenic-${spot.id}`,\r\n\t\t\t\timage: SCENIC_IMAGES[spot.folder].index\r\n\t\t\t}));\r\n\r\n\t\t\tconst foodItems = Object.values(database.food).map(food => ({\r\n\t\t\t\t...food,\r\n\t\t\t\ttype: 'food',\r\n\t\t\t\tkey: `food-${food.id}`,\r\n\t\t\t\timage: FOOD_IMAGES[food.id]\r\n\t\t\t}));\r\n\t\t\t\r\n\t\t\tconst allItems = [...scenicSpots, ...foodItems];\r\n\r\n\t\t\tthis.footprints = uni.getStorageSync('checkIns') || {}; // 使用统一的 checkIns\r\n\r\n\t\t\tthis.allItems = allItems.map(item => {\r\n\t\t\t\tconst footprint = this.footprints[item.key];\r\n\t\t\t\tconst isCheckedIn = !!footprint;\r\n\t\t\t\t\r\n\t\t\t\treturn {\r\n\t\t\t\t\t...item,\r\n\t\t\t\t\tisCheckedIn,\r\n\t\t\t\t};\r\n\t\t\t});\r\n\t\t},\r\n\t\tgoToDetail(item) {\r\n\t\t\tlet url = '';\r\n\t\t\tif (item.type === 'scenic') {\r\n\t\t\t\turl = `/pages/scenic/detail?id=${item.id}`;\r\n\t\t\t} else if (item.type === 'food') {\r\n\t\t\t\turl = `/pages/food/detail?id=${item.id}`;\r\n\t\t\t}\r\n\r\n\t\t\tif (url) {\r\n\t\t\t\tuni.navigateTo({ url });\r\n\t\t\t}\r\n\t\t},\r\n\t\tgoHome() {\r\n\t\t\tuni.reLaunch({\r\n\t\t\t\turl: '/pages/index/index'\r\n\t\t\t});\r\n\t\t},\r\n\t\tgoToQuiz() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/quiz/index'\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.star-chart-container {\r\n\tbackground-color: #0c0a18;\r\n\tbackground-image: radial-gradient(circle at 20% 20%, rgba(100, 100, 150, 0.3) 0%, transparent 40%),\r\n\t                  radial-gradient(circle at 80% 70%, rgba(150, 100, 100, 0.2) 0%, transparent 30%);\r\n\tmin-height: 100vh;\r\n\tcolor: #e0e0e0;\r\n\tpadding: 0 40rpx 40rpx 40rpx;\r\n\tbox-sizing: border-box;\r\n\tpadding-top: var(--status-bar-height);\r\n\r\n\t.back-button {\r\n\t\tposition: absolute;\r\n\t\ttop: calc(var(--status-bar-height) + 20rpx);\r\n\t\tleft: 30rpx;\r\n\t\twidth: 70rpx;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: rgba(255, 255, 255, 0.1);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 10;\r\n\t\ttransition: all 0.3s ease;\r\n\r\n\t\t.back-icon {\r\n\t\t\tfont-size: 50rpx;\r\n\t\t\tcolor: #a0a0c0;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tline-height: 70rpx; // for vertical center alignment\r\n\t\t}\r\n\r\n\t\t&:active {\r\n\t\t\tbackground: rgba(255, 215, 0, 0.2);\r\n\t\t\tbox-shadow: 0 0 15rpx rgba(255, 215, 0, 0.5);\r\n\t\t}\r\n\t}\r\n\r\n\t.header {\r\n\t\tpadding-top: 20rpx;\r\n\t\tmargin-bottom: 60rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.title {\r\n\t\tfont-size: 52rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #FFD700;\r\n\t\ttext-shadow: 0 0 10rpx #FFD700;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tfont-family: 'KaiTi', 'STKaiti', serif;\r\n\t}\r\n\r\n\t.progress-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #a0a0c0;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t\r\n\t.progress-bar {\r\n\t\tborder-radius: 4rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.star-map {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: repeat(2, 1fr); /* 改为两列以适应不同形状 */\r\n\t\tgap: 40rpx;\r\n\t}\r\n\r\n\t.grid-item {\r\n\t\tposition: relative;\r\n\t\theight: 280rpx;\r\n\t\ttransition: all 0.4s ease-in-out;\r\n\t\tbackground-color: #333;\r\n\t\tbox-shadow: 0 8rpx 20rpx rgba(0,0,0,0.5);\r\n\r\n\t\t&.scenic {\r\n\t\t\tborder-radius: 50%;\r\n\t\t}\r\n\t\t&.food {\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.item-image {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tfilter: grayscale(100%) brightness(0.4);\r\n\t\t\ttransition: all 0.5s ease;\r\n\t\t}\r\n\r\n\t\t&.scenic .item-image { border-radius: 50%; }\r\n\t\t&.food .item-image { border-radius: 16rpx; }\r\n\t\t\r\n\t\t.item-overlay {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground: radial-gradient(circle, transparent 50%, rgba(0,0,0,0.8) 100%);\r\n\t\t}\r\n\t\t\r\n\t\t&.scenic .item-overlay { border-radius: 50%; }\r\n\t\t&.food .item-overlay { border-radius: 16rpx; }\r\n\r\n\t\t.item-name {\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 20rpx;\r\n\t\t\tleft: 50%;\r\n\t\t\ttransform: translateX(-50%);\r\n\t\t\twidth: 100%;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #bbb;\r\n\t\t\tfont-weight: bold;\r\n\t\t\ttransition: color 0.5s ease;\r\n\t\t}\r\n\r\n\t\t.lock-icon {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 50%;\r\n\t\t\tleft: 50%;\r\n\t\t\ttransform: translate(-50%, -50%);\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: rgba(255, 215, 0, 0.4);\r\n\t\t\tbackground-color: rgba(0,0,0,0.5);\r\n\t\t\tpadding: 10rpx 20rpx;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\ttext-shadow: 0 0 5rpx rgba(0,0,0,0.5);\r\n\t\t\ttransition: all 0.5s ease;\r\n\t\t}\r\n\r\n\t\t&.unlocked {\r\n\t\t\t.item-image {\r\n\t\t\t\tfilter: grayscale(0%) brightness(1);\r\n\t\t\t}\r\n\t\t\t&.scenic .item-image {\r\n\t\t\t\tbox-shadow: 0 0 15rpx 5rpx rgba(255, 215, 0, 0.4),\r\n\t\t\t\t            0 0 30rpx 10rpx rgba(255, 215, 0, 0.2);\r\n\t\t\t}\r\n\t\t\t&.food .item-image {\r\n\t\t\t\tbox-shadow: 0 0 15rpx 5rpx rgba(100, 255, 100, 0.4);\r\n\t\t\t}\r\n\t\t\t.item-name {\r\n\t\t\t\tcolor: #FFD700;\r\n\t\t\t}\r\n\t\t\t.lock-icon {\r\n\t\t\t\topacity: 0;\r\n\t\t\t\ttransform: translate(-50%, -50%) scale(0);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&:active {\r\n\t\t\ttransform: scale(0.95);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.action-button-container {\r\n\tmargin-top: 80rpx;\r\n\ttext-align: center;\r\n}\r\n\r\n.portal-button {\r\n\tdisplay: inline-block;\r\n\tbackground: radial-gradient(ellipse at center, rgba(255, 215, 0, 0.8) 0%, rgba(255, 215, 0, 0) 70%);\r\n\tcolor: #fff;\r\n\tfont-weight: bold;\r\n\tpadding: 25rpx 70rpx;\r\n\tborder-radius: 50rpx;\r\n\tbox-shadow: 0 0 20rpx rgba(255, 215, 0, 0.5);\r\n\tborder: 1rpx solid rgba(255, 215, 0, 0.3);\r\n\ttransition: all 0.3s ease;\r\n\t\r\n\t&:active {\r\n\t\ttransform: scale(0.98);\r\n\t\tbox-shadow: 0 0 30rpx rgba(255, 215, 0, 0.8);\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/Users/<USER>/Desktop/yunVr/pages/profile/footprints.vue'\nwx.createPage(MiniProgramPage)"], "names": ["database", "SCENIC_IMAGES", "FOOD_IMAGES", "uni"], "mappings": ";;;;AAwCA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,UAAU,CAAE;AAAA,MACZ,YAAY,CAAE;AAAA;EAEf;AAAA,EACD,UAAU;AAAA,IACT,iBAAiB;AAChB,aAAO,KAAK,SAAS,OAAO,OAAK,EAAE,WAAW,EAAE;AAAA,IAChD;AAAA,IACD,aAAa;AACZ,aAAO,KAAK,SAAS;AAAA,IACrB;AAAA,IACD,kBAAkB;AACjB,aAAO,KAAK,aAAa,IAAK,KAAK,iBAAiB,KAAK,aAAc,MAAM;AAAA,IAC9E;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,SAAQ;AAAA,EACb;AAAA,EACD,SAAS;AAAA,IACR,WAAW;AACV,YAAM,cAAc,OAAO,OAAOA,gBAAQ,SAAC,MAAM,EAAE,IAAI,WAAS;AAAA,QAC/D,GAAG;AAAA,QACH,MAAM;AAAA,QACN,KAAK,UAAU,KAAK,EAAE;AAAA,QACtB,OAAOC,cAAa,cAAC,KAAK,MAAM,EAAE;AAAA,MAClC,EAAC;AAEF,YAAM,YAAY,OAAO,OAAOD,gBAAQ,SAAC,IAAI,EAAE,IAAI,WAAS;AAAA,QAC3D,GAAG;AAAA,QACH,MAAM;AAAA,QACN,KAAK,QAAQ,KAAK,EAAE;AAAA,QACpB,OAAOE,cAAAA,YAAY,KAAK,EAAE;AAAA,MAC1B,EAAC;AAEF,YAAM,WAAW,CAAC,GAAG,aAAa,GAAG,SAAS;AAE9C,WAAK,aAAaC,cAAG,MAAC,eAAe,UAAU,KAAK,CAAA;AAEpD,WAAK,WAAW,SAAS,IAAI,UAAQ;AACpC,cAAM,YAAY,KAAK,WAAW,KAAK,GAAG;AAC1C,cAAM,cAAc,CAAC,CAAC;AAEtB,eAAO;AAAA,UACN,GAAG;AAAA,UACH;AAAA;MAEF,CAAC;AAAA,IACD;AAAA,IACD,WAAW,MAAM;AAChB,UAAI,MAAM;AACV,UAAI,KAAK,SAAS,UAAU;AAC3B,cAAM,2BAA2B,KAAK,EAAE;AAAA,iBAC9B,KAAK,SAAS,QAAQ;AAChC,cAAM,yBAAyB,KAAK,EAAE;AAAA,MACvC;AAEA,UAAI,KAAK;AACRA,sBAAAA,MAAI,WAAW,EAAE,IAAE,CAAG;AAAA,MACvB;AAAA,IACA;AAAA,IACD,SAAS;AACRA,oBAAAA,MAAI,SAAS;AAAA,QACZ,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA,IACD,WAAW;AACVA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjHA,GAAG,WAAW,eAAe;"}