{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "../../../../App/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"home-view\">\r\n\t\t<swiper class=\"swiper\" vertical>\r\n\t\t\t<swiper-item class=\"swiper-item\">\r\n\t\t\t\t<view class=\"item-box\">\r\n\t\t\t\t\t<!-- 背景图片 -->\r\n\t\t\t\t\t<image class=\"bg-image\" :src=\"scenicImages.bg\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t<!-- 背景遮罩层 -->\r\n\t\t\t\t\t<view class=\"bg-overlay\"></view>\r\n\t\t\t\t\t<view class=\"swiper-windows\" @click=\"goToScenicDetail()\">\r\n\t\t\t\t\t\t<!-- 衡阳六大景点轮播图 -->\r\n\t\t\t\t\t\t<image \r\n\t\t\t\t\t\t\tv-for=\"spot in scenicSpots\"\r\n\t\t\t\t\t\t\t:key=\"spot.id\"\r\n\t\t\t\t\t\t\tclass=\"windows-image\" \r\n\t\t\t\t\t\t\t:style=\"{ \r\n\t\t\t\t\t\t\t\topacity: status == spot.id ? 1 : 0,\r\n\t\t\t\t\t\t\t\tzIndex: 7 - spot.id \r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\t:src=\"scenicImages[spot.folder].index\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t<!-- 窗口遮罩和背景 -->\r\n\t\t\t\t\t\t<view class=\"windows-mask\"></view>\r\n\t\t\t\t\t\t<view class=\"windows-bg\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 天气徽章 -->\r\n\t\t\t\t<view class=\"weather-badge\" @click=\"getWeather\" v-if=\"weather.city\">\r\n\t\t\t\t\t<view class=\"weather-icon\">{{ weather.icon }}</view>\r\n\t\t\t\t\t<view class=\"weather-info\">\r\n\t\t\t\t\t\t<text class=\"temperature\">{{ weather.temperature }}°C</text>\r\n\t\t\t\t\t\t<text class=\"city-name\">{{ weather.city }} · {{ weather.condition }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 标题图片 -->\r\n\t\t\t\t<view class=\"title-image\">\r\n\t\t\t\t\t<text class=\"title-text\">衡阳古韵</text>\r\n\t\t\t\t\t<text class=\"subtitle-text\">探寻千年文化之美</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 提示框 -->\r\n\t\t\t\t<swiper class=\"recommend-swiper\" :indicator-dots=\"false\" autoplay=\"true\" interval=\"4000\" duration=\"1000\" circular=\"true\">\r\n\t\t\t\t\t<swiper-item v-for=\"item in recommendations\" :key=\"item.id\" @click=\"goToRecommendation(item)\">\r\n\t\t\t\t\t\t<view class=\"recommend-item\">\r\n\t\t\t\t\t\t\t<text class=\"recommend-icon\">{{ item.icon }}</text>\r\n\t\t\t\t\t\t\t<view class=\"recommend-text-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"recommend-title\">{{ item.title }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"recommend-subtitle\">{{ item.subtitle }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</swiper>\r\n\t\t\t\t<!-- 按钮 -->\r\n\t\t\t\t<view class=\"button-group\">\r\n\t\t\t\t\t<view class=\"footprints-btn\" @click=\"goToFootprints()\">\r\n\t\t\t\t\t\t<text class=\"btn-icon\">印</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"explore-btn primary\" @click=\"handleExploreClick()\">\r\n\t\t\t\t\t\t<text class=\"btn-text\">🗺️ 衡阳风味</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</swiper-item>\r\n\t\t</swiper>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { SCENIC_IMAGES } from '@/common/config.js';\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tstatus: 1,\r\n\t\t\t\ttimes: null,\r\n\t\t\t\tweather: {}, // 天气数据\r\n\t\t\t\tscenicImages: SCENIC_IMAGES, // 引入配置\r\n\t\t\t\tscenicSpots: [\r\n\t\t\t\t\t{ id: 1, name: '南岳衡山', folder: 'hengshan' },\r\n\t\t\t\t\t{ id: 2, name: '石鼓书院', folder: 'shigu' },\r\n\t\t\t\t\t{ id: 3, name: '回雁峰', folder: 'huiyan' },\r\n\t\t\t\t\t{ id: 4, name: '南华大学', folder: 'nanhua' },\r\n\t\t\t\t\t{ id: 5, name: '东洲岛', folder: 'dongzhou' },\r\n\t\t\t\t\t{ id: 6, name: '岣嵝峰', folder: 'goulou' }\r\n\t\t\t\t],\r\n\t\t\t\trecommendations: [\r\n\t\t\t\t\t{ id: 1, icon: '🌟', title: '今日推荐', subtitle: '南岳衡山 - 五岳独秀', targetId: 1 },\r\n\t\t\t\t\t{ id: 2, icon: '📚', title: '文化之旅', subtitle: '石鼓书院 - 千年学府', targetId: 2 },\r\n\t\t\t\t\t{ id: 3, icon: '🐦', title: '登高望远', subtitle: '回雁峰 - 雁城之巅', targetId: 3 },\r\n\t\t\t\t\t{ id: 5, icon: '🍃', title: '江心绿洲', subtitle: '东洲岛 - 城市氧吧', targetId: 5 },\r\n\t\t\t\t]\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad() {\r\n\t\t\tthis.startCarousel()\r\n\t\t\tthis.getWeather()\r\n\t\t},\r\n\r\n\t\tonUnload() {\r\n\t\t\tthis.stopCarousel()\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// 开始轮播\r\n\t\t\tstartCarousel() {\r\n\t\t\t\tthis.times = setInterval(() => {\r\n\t\t\t\t\tthis.setWindowsImage()\r\n\t\t\t\t}, 3000)\r\n\t\t\t},\r\n\r\n\t\t\t// 停止轮播\r\n\t\t\tstopCarousel() {\r\n\t\t\t\tif (this.times) {\r\n\t\t\t\t\tclearInterval(this.times)\r\n\t\t\t\t\tthis.times = null\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 切换轮播图\r\n\t\t\tsetWindowsImage() {\r\n\t\t\t\tif (this.status == 6) {\r\n\t\t\t\t\tthis.status = 1\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.status++\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 跳转到景点详情（点击轮播图）\r\n\t\t\tgoToScenicDetail() {\r\n\t\t\t\tconst currentScenic = this.scenicSpots[this.status - 1]\r\n\t\t\t\tconsole.log('跳转到景点地图:', currentScenic)\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/map/map?scenicId=${currentScenic.id}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 跳转到推荐景点\r\n\t\t\tgoToRecommendation(item) {\r\n\t\t\t\tconsole.log('跳转到推荐景点:', item.subtitle)\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/discover/index?id=${item.targetId}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 跳转到足迹页面\r\n\t\t\tgoToFootprints() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/profile/footprints'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 处理提示框点击事件（预留扩展功能）\r\n\t\t\thandleTipsClick() {\r\n\t\t\t\tconsole.log('提示框被点击 - 预留功能扩展')\r\n\t\t\t\t// TODO: 在这里添加新功能\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '功能开发中...',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 处理立即探索按钮点击事件\r\n\t\t\thandleExploreClick() {\r\n\t\t\t\tconsole.log('立即探索按钮被点击，跳转到美食页面');\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/food/index'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 获取天气信息\r\n\t\t\tgetWeather() {\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: 'https://v.api.aa1.cn/api/api-tianqi-3/index.php?msg=衡阳&type=1',\r\n\t\t\t\t\tmethod: 'GET',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.statusCode === 200 && res.data && res.data.code === \"1\") {\r\n\t\t\t\t\t\t\tconst today = res.data.data[0];\r\n\t\t\t\t\t\t\tthis.weather = {\r\n\t\t\t\t\t\t\t\tcity: '衡阳',\r\n\t\t\t\t\t\t\t\ttemperature: today.wendu.split('～')[1], // 取最高温\r\n\t\t\t\t\t\t\t\tcondition: today.tianqi,\r\n\t\t\t\t\t\t\t\tpm: today.pm,\r\n\t\t\t\t\t\t\t\ticon: this.getWeatherIcon(today.tianqi)\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.handleWeatherError();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tthis.handleWeatherError();\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tgetWeatherIcon(condition) {\r\n\t\t\t\tif (condition.includes('晴')) return '☀️';\r\n\t\t\t\tif (condition.includes('多云')) return '☁️';\r\n\t\t\t\tif (condition.includes('阴')) return '🌥️';\r\n\t\t\t\tif (condition.includes('雨')) return '🌧️';\r\n\t\t\t\tif (condition.includes('雪')) return '❄️';\r\n\t\t\t\treturn '-';\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\thandleWeatherError() {\r\n\t\t\t\t// API失败时的默认或错误状态\r\n\t\t\t\tconsole.error(\"天气API请求失败\");\r\n\t\t\t\tthis.weather = { city: '天气', temperature: 'N/A', condition: '获取失败', icon: '❓' };\r\n\t\t\t},\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.home-view {\r\n\t\twidth: 100vw;\r\n\t\theight: 100vh;\r\n\t}\r\n\r\n\t.swiper {\r\n\t\twidth: 100vw;\r\n\t\theight: 100vh;\r\n\t}\r\n\r\n\t.swiper-item {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\r\n\t\t.item-box {\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\r\n\t\t.bg-image {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tz-index: 0;\r\n\t\t}\r\n\r\n\t\t.bg-overlay {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground: rgba(0, 0, 0, 0.3);\r\n\t\t\tz-index: 1;\r\n\t\t}\r\n\r\n\t\t.swiper-windows {\r\n\t\t\theight: 546rpx;\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 50%;\r\n\t\t\ttransform: translateY(-50%);\r\n\t\t\twidth: 100%;\r\n\t\t\tz-index: 2;\r\n\r\n\t\t\t.windows-image {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\tleft: 50%;\r\n\t\t\t\ttransform: translate(-50%, -50%);\r\n\t\t\t\twidth: 520rpx;\r\n\t\t\t\theight: 520rpx;\r\n\t\t\t\topacity: 0;\r\n\t\t\t\ttransition: all 3s ease-in-out;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tobject-fit: cover;\r\n\t\t\t}\r\n\r\n\t\t\t.windows-bg,\r\n\t\t\t.windows-mask {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: 50%;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\ttransform: translate(-50%, -50%);\r\n\t\t\t\twidth: 546rpx;\r\n\t\t\t\theight: 546rpx;\r\n\t\t\t\tz-index: 99;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t}\r\n\r\n\t\t\t.windows-mask {\r\n\t\t\t\tbackground: rgba(0, 0, 0, 0.1);\r\n\t\t\t\tborder: 8rpx solid rgba(139, 69, 19, 0.8);\r\n\t\t\t}\r\n\r\n\t\t\t.windows-bg {\r\n\t\t\t\tbackground: rgba(139, 69, 19, 0.2);\r\n\t\t\t\tborder: 4rpx solid rgba(139, 69, 19, 0.6);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.title-image {\r\n\t\tposition: absolute;\r\n\t\tbottom: 25%;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\ttext-align: center;\r\n\t\tz-index: 3;\r\n\r\n\t\t.title-text {\r\n\t\t\tdisplay: block;\r\n\t\t\tfont-size: 48rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #FFD700;\r\n\t\t\ttext-shadow: 2rpx 2rpx 6rpx rgba(0, 0, 0, 0.8);\r\n\t\t\tmargin-bottom: 10rpx;\r\n\t\t}\r\n\r\n\t\t.subtitle-text {\r\n\t\t\tdisplay: block;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #FFF8DC;\r\n\t\t\ttext-shadow: 1rpx 1rpx 4rpx rgba(0, 0, 0, 0.6);\r\n\t\t}\r\n\t}\r\n\r\n\t.flex-box-space-between {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.recommend-swiper {\r\n\t\tposition: absolute;\r\n\t\tbottom: 15%;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\twidth: 702rpx;\r\n\t\theight: 120rpx; /* 增加空间感 */\r\n\t\tz-index: 3;\r\n\r\n\t\t/* vue3.0 transition not work in swiper-item, we use css animation */\r\n\t\t.swiper-item {\r\n\t\t\tanimation: fadeIn 1s ease-in-out;\r\n\t\t}\r\n\r\n\t\t@keyframes fadeIn {\r\n\t\t\tfrom { opacity: 0.5; }\r\n\t\t\tto { opacity: 1; }\r\n\t\t}\r\n\r\n\t\t.recommend-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground: rgba(0, 0, 0, 0.4); /* 暗色背景 */\r\n\t\t\tborder-radius: 16rpx; /* 更圆润的边角 */\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tborder: 1rpx solid rgba(255, 215, 0, 0.3); /* 金色辉光边框 */\r\n\t\t\tbackdrop-filter: blur(10px); /* 毛玻璃效果 */\r\n\t\t}\r\n\r\n\t\t.recommend-icon {\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t}\r\n\r\n\t\t.recommend-text-content {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t}\r\n\r\n\t\t.recommend-title {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #FFD700; /* 金色标题 */\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\r\n\t\t.recommend-subtitle {\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tcolor: #FFF8DC; /* 米白副标题 */\r\n\t\t}\r\n\t}\r\n\r\n\t/* .tips-box {\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 15%;\r\n\t\t\tleft: 50%;\r\n\t\t\ttransform: translateX(-50%);\r\n\t\t\twidth: 702rpx;\r\n\t\t\theight: 82rpx;\r\n\t\t\tbackground: rgba(255, 255, 255, 0.9);\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tbox-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);\r\n\t\t\tz-index: 3;\r\n\r\n\t\t\t.tips-icon {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.tips-text {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t}\r\n\t\t} */\r\n\r\n\t.button-group {\r\n\t\tposition: absolute;\r\n\t\tbottom: 5%;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center; /* 确保按钮垂直居中对齐 */\r\n\t\tgap: 20rpx;\r\n\t\tz-index: 3;\r\n\t}\r\n\r\n\t.explore-btn {\r\n\t\twidth: 280rpx; /* 调整宽度以适应双按钮布局 */\r\n\t\theight: 88rpx;\r\n\t\tborder-radius: 44rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbox-shadow: 0 6rpx 12rpx rgba(139, 69, 19, 0.3);\r\n\t\tbackground: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);\r\n\r\n\t\t.btn-text {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\tfont-weight: bold;\r\n\t\t\ttext-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.3);\r\n\t\t}\r\n\t}\r\n\r\n\t.footprints-btn {\r\n\t\twidth: 88rpx;\r\n\t\theight: 88rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground: rgba(255, 255, 255, 0.8);\r\n\t\tbox-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.2);\r\n\t\tbackdrop-filter: blur(5px);\r\n\t\tborder: 1rpx solid rgba(255, 215, 0, 0.5);\r\n\r\n\t\t.btn-icon {\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\tcolor: #8B4513;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t}\r\n\r\n\t.weather-badge {\r\n\t\tposition: absolute;\r\n\t\ttop: 60rpx; /* 根据你的状态栏高度调整 */\r\n\t\tleft: 30rpx;\r\n\t\tz-index: 10;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 10rpx 20rpx;\r\n\t\tbackground: rgba(0, 0, 0, 0.4);\r\n\t\tborder-radius: 30rpx;\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\tborder: 1rpx solid rgba(255, 215, 0, 0.3);\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\r\n\t\t.weather-icon {\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\tmargin-right: 15rpx;\r\n\t\t}\r\n\r\n\t\t.weather-info {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\r\n\t\t.temperature {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\r\n\t\t.city-name {\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tcolor: #eee;\r\n\t\t\topacity: 0.9;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import MiniProgramPage from 'D:/Users/<USER>/Desktop/yunVr/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["SCENIC_IMAGES", "uni"], "mappings": ";;;AAoEC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS,CAAE;AAAA;AAAA,MACX,cAAcA,cAAa;AAAA;AAAA,MAC3B,aAAa;AAAA,QACZ,EAAE,IAAI,GAAG,MAAM,QAAQ,QAAQ,WAAY;AAAA,QAC3C,EAAE,IAAI,GAAG,MAAM,QAAQ,QAAQ,QAAS;AAAA,QACxC,EAAE,IAAI,GAAG,MAAM,OAAO,QAAQ,SAAU;AAAA,QACxC,EAAE,IAAI,GAAG,MAAM,QAAQ,QAAQ,SAAU;AAAA,QACzC,EAAE,IAAI,GAAG,MAAM,OAAO,QAAQ,WAAY;AAAA,QAC1C,EAAE,IAAI,GAAG,MAAM,OAAO,QAAQ,SAAS;AAAA,MACvC;AAAA,MACD,iBAAiB;AAAA,QAChB,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,QAAQ,UAAU,eAAe,UAAU,EAAG;AAAA,QAC1E,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,QAAQ,UAAU,eAAe,UAAU,EAAG;AAAA,QAC1E,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,QAAQ,UAAU,cAAc,UAAU,EAAG;AAAA,QACzE,EAAE,IAAI,GAAG,MAAM,MAAM,OAAO,QAAQ,UAAU,cAAc,UAAU,EAAG;AAAA,MAC1E;AAAA;EAED;AAAA,EAED,SAAS;AACR,SAAK,cAAc;AACnB,SAAK,WAAW;AAAA,EAChB;AAAA,EAED,WAAW;AACV,SAAK,aAAa;AAAA,EAClB;AAAA,EAED,SAAS;AAAA;AAAA,IAER,gBAAgB;AACf,WAAK,QAAQ,YAAY,MAAM;AAC9B,aAAK,gBAAgB;AAAA,MACrB,GAAE,GAAI;AAAA,IACP;AAAA;AAAA,IAGD,eAAe;AACd,UAAI,KAAK,OAAO;AACf,sBAAc,KAAK,KAAK;AACxB,aAAK,QAAQ;AAAA,MACd;AAAA,IACA;AAAA;AAAA,IAGD,kBAAkB;AACjB,UAAI,KAAK,UAAU,GAAG;AACrB,aAAK,SAAS;AAAA,aACR;AACN,aAAK;AAAA,MACN;AAAA,IACA;AAAA;AAAA,IAGD,mBAAmB;AAClB,YAAM,gBAAgB,KAAK,YAAY,KAAK,SAAS,CAAC;AACtDC,oBAAAA,MAAA,MAAA,OAAA,gCAAY,YAAY,aAAa;AACrCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,2BAA2B,cAAc,EAAE;AAAA,OAChD;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB,MAAM;AACxBA,oBAAA,MAAA,MAAA,OAAA,gCAAY,YAAY,KAAK,QAAQ;AACrCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,4BAA4B,KAAK,QAAQ;AAAA,OAC9C;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AAChBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AACjBA,oBAAAA,MAAA,MAAA,OAAA,gCAAY,iBAAiB;AAE7BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,OACN;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACpBA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,mBAAmB;AAC/BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa;AACZA,oBAAAA,MAAI,QAAQ;AAAA,QACX,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,eAAe,OAAO,IAAI,QAAQ,IAAI,KAAK,SAAS,KAAK;AAChE,kBAAM,QAAQ,IAAI,KAAK,KAAK,CAAC;AAC7B,iBAAK,UAAU;AAAA,cACd,MAAM;AAAA,cACN,aAAa,MAAM,MAAM,MAAM,GAAG,EAAE,CAAC;AAAA;AAAA,cACrC,WAAW,MAAM;AAAA,cACjB,IAAI,MAAM;AAAA,cACV,MAAM,KAAK,eAAe,MAAM,MAAM;AAAA;iBAEjC;AACN,iBAAK,mBAAkB;AAAA,UACxB;AAAA,QACA;AAAA,QACD,MAAM,CAAC,QAAQ;AACd,eAAK,mBAAkB;AAAA,QACxB;AAAA,MACD,CAAC;AAAA,IACD;AAAA,IAED,eAAe,WAAW;AACzB,UAAI,UAAU,SAAS,GAAG;AAAG,eAAO;AACpC,UAAI,UAAU,SAAS,IAAI;AAAG,eAAO;AACrC,UAAI,UAAU,SAAS,GAAG;AAAG,eAAO;AACpC,UAAI,UAAU,SAAS,GAAG;AAAG,eAAO;AACpC,UAAI,UAAU,SAAS,GAAG;AAAG,eAAO;AACpC,aAAO;AAAA,IACP;AAAA,IAED,qBAAqB;AAEpBA,oBAAAA,MAAc,MAAA,SAAA,gCAAA,WAAW;AACzB,WAAK,UAAU,EAAE,MAAM,MAAM,aAAa,OAAO,WAAW,QAAQ,MAAM;IAC1E;AAAA,EAEF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChND,GAAG,WAAW,eAAe;"}