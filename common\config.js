// 图片资源配置 - 使用云存储避免主包过大
const IMAGE_BASE_URL = 'https://mike-koka123.oss-cn-beijing.aliyuncs.com/image/'

// 景点图片配置
export const SCENIC_IMAGES = {
	// 南岳衡山
	hengshan: {
		index: IMAGE_BASE_URL + 'hengshan/hengshanindex.png',
		mark: IMAGE_BASE_URL + 'hengshan/hengshanmark.jpg', 
		view: IMAGE_BASE_URL + 'hengshan/hengshanview.jpg'
	},
	// 石鼓书院
	shigu: {
		index: IMAGE_BASE_URL + 'shigu/shiguindex.png',
		mark: IMAGE_BASE_URL + 'shigu/shigumark.jpg',
		view: IMAGE_BASE_URL + 'shigu/shiguview.jpg'
	},
	// 回雁峰
	huiyan: {
		index: IMAGE_BASE_URL + 'huiyan/huiyanindex.jpg',
		mark: IMAGE_BASE_URL + 'huiyan/huiyanmark.jpg',
		view: IMAGE_BASE_URL + 'huiyan/huiyanview.jpg'
	},
	// 南华大学
	nanhua: {
		index: IMAGE_BASE_URL + 'nanhua/nanhuaindex.png',
		mark: IMAGE_BASE_URL + 'nanhua/nanhuamark.jpg',
		view: IMAGE_BASE_URL + 'nanhua/nanhuaview.jpg'
	},
	// 东洲岛
	dongzhou: {
		index: IMAGE_BASE_URL + 'dongzhou/dongzhouindex.jpg',
		mark: IMAGE_BASE_URL + 'dongzhou/dongzhoumark.jpg',
		view: IMAGE_BASE_URL + 'dongzhou/dongzhouview.jpg'
	},
	// 岣嵝峰
	goulou: {
		index: IMAGE_BASE_URL + 'goulou/goullouindex.png',
		mark: IMAGE_BASE_URL + 'goulou/gouloumark.jpg',
		view: IMAGE_BASE_URL + 'goulou/goulouview.jpg'
	},
	// 通用背景
	bg: IMAGE_BASE_URL + 'bg.jpg'
}

// 美食图片配置
export const FOOD_IMAGES = {
	index: IMAGE_BASE_URL + '美食/美食推荐/foodindex.jpg',
	xiaogurou: IMAGE_BASE_URL + '美食/青椒削骨肉/xiaogurou.jpg',
	yufen: IMAGE_BASE_URL + '美食/衡阳鱼粉/yufen.jpg',
	tutouwan: IMAGE_BASE_URL + '美食/衡阳土头碗/tutouwan.jpg',
	cuidu: IMAGE_BASE_URL + '美食/衡东脆肚/cuidu.jpg',
	tuji: IMAGE_BASE_URL + '美食/茶油炒土鸡/tuji.jpg',
	fuzirou: IMAGE_BASE_URL + '美食/杨桥麸子肉/fuzirou.jpg',
}

// 图片加载工具函数
export const getImageUrl = (scenicKey, type) => {
	return SCENIC_IMAGES[scenicKey] && SCENIC_IMAGES[scenicKey][type] 
		? SCENIC_IMAGES[scenicKey][type]
		: IMAGE_BASE_URL + 'default.jpg'
}

// 美食图片加载工具函数
export const getFoodImageUrl = (foodKey) => {
	return FOOD_IMAGES[foodKey] || IMAGE_BASE_URL + 'default_food.jpg';
} 