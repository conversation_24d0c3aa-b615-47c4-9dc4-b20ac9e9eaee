<view wx:if="{{a}}" class="discover-page"><view class="page-bg" style="{{'background-image:' + b}}"></view><view class="page-bg-overlay"></view><view class="discover-container"><view class="back-button" bindtap="{{c}}"><view class="arrow"></view></view><view class="featured-card" style="{{'background-image:' + h + ';' + ('background-position:' + i)}}"><view class="card-overlay"></view><view class="card-content"><text class="card-title">{{d}}</text><text class="card-subtitle">{{e}}</text><view class="card-buttons"><button class="card-btn detail" bindtap="{{f}}">查看详情</button><button class="card-btn vr" bindtap="{{g}}">VR体验</button></view></view></view><view class="ai-chat-container"><scroll-view scroll-y="{{true}}" class="chat-history" scroll-top="{{l}}"><view wx:for="{{j}}" wx:for-item="message" wx:key="d" class="{{['message-wrapper', message.e]}}"><view class="{{['avatar', message.b]}}">{{message.a}}</view><view class="message-content"><text user-select="{{true}}">{{message.c}}</text></view></view><view wx:if="{{k}}" class="message-wrapper assistant"><view class="avatar assistant">通</view><view class="message-content loading"><view class="dot-flashing"></view></view></view></scroll-view><view class="input-area"><input type="text" placeholder="聊聊衡阳的风土人情..." class="input-field" disabled="{{m}}" bindconfirm="{{n}}" value="{{o}}" bindinput="{{p}}"/><button bindtap="{{q}}" class="send-button" disabled="{{r}}">发送</button></view></view></view></view>