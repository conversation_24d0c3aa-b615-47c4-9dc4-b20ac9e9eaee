"use strict";
const common_vendor = require("../../common/vendor.js");
const common_config = require("../../common/config.js");
const common_database = require("../../common/database.js");
const _sfc_main = {
  data() {
    return {
      scenicId: null,
      scrollY: 0,
      scrollTopValue: 0,
      activeSection: -1,
      activeTimelineItem: -1,
      activeCultureCard: -1,
      showNavbar: false,
      compareSliderPosition: 50,
      hasCheckedIn: false,
      showCultureModal: false,
      selectedCulture: {},
      touchStartY: 0,
      scenic: {}
    };
  },
  computed: {
    headerOffset() {
      return this.scrollY * 0.5;
    },
    titleOffset() {
      return this.scrollY * 0.3;
    },
    subtitleOffset() {
      return this.scrollY * 0.4;
    },
    titleOpacity() {
      return Math.max(0, 1 - this.scrollY / 300);
    },
    subtitleOpacity() {
      return Math.max(0, 1 - this.scrollY / 200);
    },
    scrollProgress() {
      return Math.min(100, this.scrollY / 2e3 * 100);
    }
  },
  onLoad(options) {
    if (options.id) {
      this.scenicId = parseInt(options.id);
      this.loadScenicData(this.scenicId);
    }
    this.initAnimations();
  },
  methods: {
    loadScenicData(id) {
      const data = common_database.database.scenic[id];
      if (data) {
        const scenicData = { ...data };
        scenicData.viewImage = common_config.SCENIC_IMAGES[scenicData.folder].view;
        scenicData.historicalImage = common_config.SCENIC_IMAGES[scenicData.folder].index;
        this.scenic = scenicData;
        this.checkFootprintStatus();
      }
    },
    initAnimations() {
      setTimeout(() => {
        this.activeSection = 4;
        this.activeTimelineItem = 3;
        this.activeCultureCard = 3;
      }, 500);
    },
    onScroll(e) {
      this.scrollY = e.detail.scrollTop;
      this.showNavbar = this.scrollY > 200;
    },
    onTouchStart(e) {
      this.touchStartY = e.touches[0].clientY;
    },
    onCompareSlide(e) {
      const touch = e.touches[0];
      const percentage = touch.clientX / common_vendor.index.getSystemInfoSync().windowWidth * 100;
      this.compareSliderPosition = Math.max(0, Math.min(100, percentage));
    },
    showCultureDetail(culture) {
      this.selectedCulture = culture;
      this.showCultureModal = true;
    },
    closeCultureModal() {
      this.showCultureModal = false;
    },
    startVRExperience() {
      common_vendor.index.navigateTo({ url: `/pages/vr/experience?scenicId=${this.scenic.id}&name=${encodeURIComponent(this.scenic.name)}` });
    },
    showOnMap() {
      common_vendor.index.navigateTo({ url: `/pages/map/map?scenicId=${this.scenic.id}` });
    },
    shareScenic() {
      common_vendor.index.showToast({ title: "分享功能开发中", icon: "none" });
    },
    checkIn() {
      const key = `scenic-${this.scenic.id}`;
      const checkIns = common_vendor.index.getStorageSync("checkIns") || {};
      if (this.hasCheckedIn) {
        common_vendor.index.showModal({
          title: "取消打卡",
          content: "确定要抹除这个足迹吗？",
          success: (res) => {
            if (res.confirm) {
              delete checkIns[key];
              common_vendor.index.setStorageSync("checkIns", checkIns);
              this.hasCheckedIn = false;
              common_vendor.index.showToast({ title: "足迹已抹除", icon: "none" });
            }
          }
        });
      } else {
        checkIns[key] = { id: this.scenic.id, type: "scenic", name: this.scenic.name, checkedInAt: (/* @__PURE__ */ new Date()).toISOString() };
        common_vendor.index.setStorageSync("checkIns", checkIns);
        this.hasCheckedIn = true;
        common_vendor.index.showToast({ title: "盖章成功！", icon: "success" });
      }
    },
    checkFootprintStatus() {
      const checkIns = common_vendor.index.getStorageSync("checkIns") || {};
      const key = `scenic-${this.scenic.id}`;
      this.hasCheckedIn = !!checkIns[key];
    },
    goBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.scenic.viewImage,
    c: `scale(${1 + $data.scrollY * 5e-4})`,
    d: common_vendor.t($data.scenic.name),
    e: `translateY(${$options.titleOffset}px)`,
    f: $options.titleOpacity,
    g: common_vendor.t($data.scenic.category),
    h: common_vendor.t($data.scenic.era),
    i: `translateY(${$options.subtitleOffset}px)`,
    j: $options.subtitleOpacity,
    k: common_vendor.t($data.scenic.name),
    l: common_vendor.o((...args) => $options.shareScenic && $options.shareScenic(...args)),
    m: $data.showNavbar ? 1 : "",
    n: `translateY(${$options.headerOffset}px)`,
    o: $options.scrollProgress + "%",
    p: common_vendor.t($data.scenic.introduction),
    q: common_vendor.f($data.scenic.tags, (tag, k0, i0) => {
      return {
        a: common_vendor.t(tag),
        b: tag
      };
    }),
    r: $data.activeSection >= 0 ? 1 : "",
    s: common_vendor.f($data.scenic.history, (period, index, i0) => {
      return {
        a: common_vendor.t(period.era),
        b: common_vendor.t(period.description),
        c: index,
        d: $data.activeTimelineItem >= index ? 1 : "",
        e: index * 0.2 + "s"
      };
    }),
    t: $data.activeSection >= 1 ? 1 : "",
    v: common_vendor.f($data.scenic.culture, (aspect, index, i0) => {
      return {
        a: common_vendor.t(aspect.icon),
        b: common_vendor.t(aspect.name),
        c: common_vendor.t(aspect.description),
        d: index,
        e: $data.activeCultureCard >= index ? 1 : "",
        f: common_vendor.o(($event) => $options.showCultureDetail(aspect), index)
      };
    }),
    w: $data.activeSection >= 2 ? 1 : "",
    x: common_vendor.t($data.compareSliderPosition < 50 ? "古韵悠长" : "今日风华"),
    y: $data.compareSliderPosition + "%",
    z: $data.scenic.historicalImage,
    A: $data.scenic.viewImage,
    B: `polygon(${$data.compareSliderPosition}% 0%, 100% 0%, 100% 100%, ${$data.compareSliderPosition}% 100%)`,
    C: common_vendor.o((...args) => $options.onCompareSlide && $options.onCompareSlide(...args)),
    D: $data.activeSection >= 3 ? 1 : "",
    E: common_vendor.t($data.scenic.bestTime),
    F: common_vendor.t($data.scenic.ticketInfo),
    G: common_vendor.t($data.scenic.transportation),
    H: common_vendor.t($data.scenic.tips),
    I: $data.activeSection >= 4 ? 1 : "",
    J: common_vendor.o((...args) => $options.showOnMap && $options.showOnMap(...args)),
    K: common_vendor.o((...args) => $options.startVRExperience && $options.startVRExperience(...args)),
    L: common_vendor.o((...args) => $options.shareScenic && $options.shareScenic(...args)),
    M: $data.hasCheckedIn ? 1 : "",
    N: common_vendor.t($data.hasCheckedIn ? "已打卡" : "盖章打卡"),
    O: common_vendor.o((...args) => $options.checkIn && $options.checkIn(...args)),
    P: common_vendor.o((...args) => $options.onScroll && $options.onScroll(...args)),
    Q: $data.scrollTopValue,
    R: $data.showCultureModal
  }, $data.showCultureModal ? {
    S: common_vendor.t($data.selectedCulture.name),
    T: common_vendor.o((...args) => $options.closeCultureModal && $options.closeCultureModal(...args)),
    U: common_vendor.t($data.selectedCulture.detailDescription),
    V: common_vendor.o(() => {
    }),
    W: common_vendor.o((...args) => $options.closeCultureModal && $options.closeCultureModal(...args))
  } : {}, {
    X: common_vendor.o((...args) => $options.onTouchStart && $options.onTouchStart(...args)),
    Y: common_vendor.o((...args) => _ctx.onTouchEnd && _ctx.onTouchEnd(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/scenic/detail.js.map
