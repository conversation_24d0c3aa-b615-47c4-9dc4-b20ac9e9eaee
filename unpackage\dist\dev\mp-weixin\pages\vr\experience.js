"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      scenicId: null,
      scenicName: "",
      vrUrl: "",
      isLoading: true,
      hasError: false,
      // VR链接映射
      vrLinks: {
        1: "https://www.720yun.com/t/9dcjvzmkea7?scene_id=20012473",
        // 南岳衡山
        2: "https://www.720yun.com/t/38cjt7umvO2?scene_id=14943450",
        // 石鼓书院
        3: "https://www.720yun.com/t/899jv7yOuy3?scene_id=21330843",
        // 回雁峰
        4: "https://www.720yun.com/t/3a62cmz8cbn?scene_id=543271",
        // 南华大学
        5: "https://www.720yun.com/t/a4vki9ryg87?scene_id=34031063",
        // 东洲岛
        6: "https://www.720yun.com/t/25vkib1h729?scene_id=37482643"
        // 岣嵝峰
      },
      // 景点名称映射
      scenicNames: {
        1: "南岳衡山",
        2: "石鼓书院",
        3: "回雁峰",
        4: "南华大学",
        5: "东洲岛",
        6: "岣嵝峰"
      }
    };
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/vr/experience.vue:88", "VR页面参数:", options);
    this.scenicId = parseInt(options.scenicId) || 1;
    this.scenicName = this.scenicNames[this.scenicId] || "未知景点";
    this.vrUrl = this.vrLinks[this.scenicId];
    if (!this.vrUrl) {
      this.hasError = true;
      this.isLoading = false;
      return;
    }
    common_vendor.index.setNavigationBarTitle({
      title: `${this.scenicName} VR体验`
    });
    common_vendor.index.__f__("log", "at pages/vr/experience.vue:106", "准备加载VR:", this.vrUrl);
  },
  methods: {
    // Web-view加载完成
    onLoad() {
      common_vendor.index.__f__("log", "at pages/vr/experience.vue:112", "VR页面加载完成");
      this.isLoading = false;
      this.hasError = false;
    },
    // Web-view加载错误
    onError(e) {
      common_vendor.index.__f__("error", "at pages/vr/experience.vue:119", "VR页面加载错误:", e);
      this.isLoading = false;
      this.hasError = true;
      common_vendor.index.showToast({
        title: "VR体验加载失败",
        icon: "none"
      });
    },
    // 接收Web-view消息
    onMessage(e) {
      common_vendor.index.__f__("log", "at pages/vr/experience.vue:131", "收到VR页面消息:", e);
    },
    // 重新加载
    retryLoad() {
      this.isLoading = true;
      this.hasError = false;
      const currentUrl = this.vrUrl;
      this.vrUrl = "";
      this.$nextTick(() => {
        this.vrUrl = currentUrl;
      });
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack({
        fail: () => {
          common_vendor.index.reLaunch({
            url: "/pages/index/index"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.t($data.scenicName),
    c: $data.isLoading
  }, $data.isLoading ? {
    d: common_vendor.t($data.scenicName)
  } : {}, {
    e: $data.vrUrl
  }, $data.vrUrl ? {
    f: $data.vrUrl,
    g: common_vendor.o((...args) => $options.onMessage && $options.onMessage(...args)),
    h: common_vendor.o((...args) => $options.onLoad && $options.onLoad(...args)),
    i: common_vendor.o((...args) => $options.onError && $options.onError(...args))
  } : {}, {
    j: $data.hasError
  }, $data.hasError ? {
    k: common_vendor.o((...args) => $options.retryLoad && $options.retryLoad(...args))
  } : {}, {
    l: !$data.isLoading && !$data.hasError
  }, !$data.isLoading && !$data.hasError ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/vr/experience.js.map
