<template>
	<view class="detail-container" @touchstart="onTouchStart" @touchend="onTouchEnd">
		<view class="back-button" @click="goBack">
			<text class="back-icon">‹</text>
		</view>
		<!-- 沉浸式头部 -->
		<view class="immersive-header" :style="{ transform: `translateY(${headerOffset}px)` }">
			<image 
				:src="scenic.viewImage" 
				class="hero-image" 
				mode="aspectFill"
				:style="{ transform: `scale(${1 + scrollY * 0.0005})` }"
			></image>
			<view class="hero-overlay">
				<view class="hero-gradient"></view>
				<view class="hero-content">
					<text class="hero-title" :style="{ transform: `translateY(${titleOffset}px)`, opacity: titleOpacity }">
						{{ scenic.name }}
					</text>
					<text class="hero-subtitle" :style="{ transform: `translateY(${subtitleOffset}px)`, opacity: subtitleOpacity }">
						{{ scenic.category }} · {{ scenic.era }}
					</text>
				</view>
			</view>
			
			<!-- 浮动导航栏 -->
			<view class="floating-navbar" :class="{ 'navbar-visible': showNavbar }">
				<view class="navbar-content">
					<!-- <view class="back-btn" @click="goBack">
						<text class="back-icon">←</text>
					</view> -->
					<text class="navbar-title">{{ scenic.name }}</text>
					<view class="share-btn" @click="shareScenic">
						<text class="share-icon">⚡</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 滚动进度指示器 -->
		<view class="scroll-progress">
			<view class="progress-bar" :style="{ width: scrollProgress + '%' }"></view>
		</view>

		<!-- 卷轴内容区域 -->
		<scroll-view 
			class="scroll-content" 
			scroll-y="true" 
			@scroll="onScroll"
			:scroll-top="scrollTopValue"
			scroll-with-animation="true"
		>
			<!-- 内容间隔 -->
			<view class="content-spacer"></view>
			
			<!-- 卷轴展开动画区域 -->
			<view class="scroll-container">
				
				<!-- 第一卷：基本信息 -->
				<view class="scroll-section intro-section" :class="{ 'section-active': activeSection >= 0 }">
					<view class="section-header">
						<view class="section-number">壹</view>
						<text class="section-title">古韵初识</text>
					</view>
					<view class="intro-card">
						<view class="card-ornament top-left"></view>
						<view class="card-ornament top-right"></view>
						<view class="card-ornament bottom-left"></view>
						<view class="card-ornament bottom-right"></view>
						
						<view class="intro-content">
							<text class="intro-text">{{ scenic.introduction }}</text>
							<view class="intro-tags">
								<text v-for="tag in scenic.tags" :key="tag" class="tag">{{ tag }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 第二卷：历史传承 -->
				<view class="scroll-section history-section" :class="{ 'section-active': activeSection >= 1 }">
					<view class="section-header">
						<view class="section-number">贰</view>
						<text class="section-title">历史传承</text>
					</view>
					<view class="timeline-container">
						<view class="timeline-line"></view>
						<view 
							v-for="(period, index) in scenic.history" 
							:key="index"
							class="timeline-item"
							:class="{ 'timeline-active': activeTimelineItem >= index }"
							:style="{ '--delay': index * 0.2 + 's' }"
						>
							<view class="timeline-dot"></view>
							<view class="timeline-content">
								<text class="timeline-period">{{ period.era }}</text>
								<text class="timeline-desc">{{ period.description }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 第三卷：文化内涵 -->
				<view class="scroll-section culture-section" :class="{ 'section-active': activeSection >= 2 }">
					<view class="section-header">
						<view class="section-number">叁</view>
						<text class="section-title">文化内涵</text>
					</view>
					<view class="culture-grid">
						<view 
							v-for="(aspect, index) in scenic.culture" 
							:key="index"
							class="culture-card"
							:class="{ 'card-active': activeCultureCard >= index }"
							@click="showCultureDetail(aspect)"
						>
							<view class="culture-icon">{{ aspect.icon }}</view>
							<text class="culture-name">{{ aspect.name }}</text>
							<text class="culture-desc">{{ aspect.description }}</text>
						</view>
					</view>
				</view>

				<!-- 第四卷：今昔对比 -->
				<view class="scroll-section compare-section" :class="{ 'section-active': activeSection >= 3 }">
					<view class="section-header">
						<view class="section-number">肆</view>
						<text class="section-title">古今对话</text>
					</view>
					<view class="compare-container">
						<view class="compare-slider" :style="{ left: compareSliderPosition + '%' }">
							<view class="slider-handle"></view>
							<text class="slider-label">{{ compareSliderPosition < 50 ? '古韵悠长' : '今日风华' }}</text>
						</view>
						<view class="compare-images" @touchmove="onCompareSlide">
							<image :src="scenic.historicalImage" class="compare-image historical" mode="aspectFill"></image>
							<image 
								:src="scenic.viewImage" 
								class="compare-image modern" 
								mode="aspectFill"
								:style="{ clipPath: `polygon(${compareSliderPosition}% 0%, 100% 0%, 100% 100%, ${compareSliderPosition}% 100%)` }"
							></image>
						</view>
					</view>
				</view>

				<!-- 第五卷：游览指南 -->
				<view class="scroll-section guide-section" :class="{ 'section-active': activeSection >= 4 }">
					<view class="section-header">
						<view class="section-number">伍</view>
						<text class="section-title">游览指南</text>
					</view>
					<view class="guide-content">
						<view class="guide-item">
							<view class="guide-icon">🕐</view>
							<view class="guide-text">
								<text class="guide-label">最佳时节</text>
								<text class="guide-value">{{ scenic.bestTime }}</text>
							</view>
						</view>
						<view class="guide-item">
							<view class="guide-icon">🎫</view>
							<view class="guide-text">
								<text class="guide-label">门票信息</text>
								<text class="guide-value">{{ scenic.ticketInfo }}</text>
							</view>
						</view>
						<view class="guide-item">
							<view class="guide-icon">🚌</view>
							<view class="guide-text">
								<text class="guide-label">交通指南</text>
								<text class="guide-value">{{ scenic.transportation }}</text>
							</view>
						</view>
						<view class="guide-item">
							<view class="guide-icon">⭐</view>
							<view class="guide-text">
								<text class="guide-label">游览建议</text>
								<text class="guide-value">{{ scenic.tips }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 底部操作区 -->
				<view class="bottom-actions">
					<view class="action-grid">
						<view class="action-item map-back-item" @click="showOnMap">
							<view class="action-icon map-icon">🗺️</view>
							<text class="action-text">地图定位</text>
						</view>
						<view class="action-item" @click="startVRExperience">
							<view class="action-icon vr-icon">🥽</view>
							<text class="action-text">VR体验</text>
						</view>
						<view class="action-item" @click="shareScenic">
							<view class="action-icon share-icon">📤</view>
							<text class="action-text">分享推荐</text>
						</view>
						<view class="action-item" @click="checkIn">
							<view class="action-icon checkin-icon" :class="{ 'checked-in': hasCheckedIn }">印</view>
							<text class="action-text">{{ hasCheckedIn ? '已打卡' : '盖章打卡' }}</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 文化详情弹窗 -->
		<view class="culture-modal" v-if="showCultureModal" @click="closeCultureModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">{{ selectedCulture.name }}</text>
					<view class="modal-close" @click="closeCultureModal">×</view>
				</view>
				<view class="modal-body">
					<text class="modal-text">{{ selectedCulture.detailDescription }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { SCENIC_IMAGES } from '@/common/config.js';
import { database } from '@/common/database.js';

export default {
	data() {
		return {
			scenicId: null,
			scrollY: 0,
			scrollTopValue: 0, 
			activeSection: -1,
			activeTimelineItem: -1,
			activeCultureCard: -1,
			showNavbar: false,
			compareSliderPosition: 50,
			hasCheckedIn: false,
			showCultureModal: false,
			selectedCulture: {},
			touchStartY: 0,
			scenic: {}
		}
	},
	computed: {
		headerOffset() { return this.scrollY * 0.5 },
		titleOffset() { return this.scrollY * 0.3 },
		subtitleOffset() { return this.scrollY * 0.4 },
		titleOpacity() { return Math.max(0, 1 - this.scrollY / 300) },
		subtitleOpacity() { return Math.max(0, 1 - this.scrollY / 200) },
		scrollProgress() { return Math.min(100, (this.scrollY / 2000) * 100) }
	},
	onLoad(options) {
		if (options.id) {
			this.scenicId = parseInt(options.id)
			this.loadScenicData(this.scenicId)
		}
		this.initAnimations()
	},
	methods: {
		loadScenicData(id) {
			const data = database.scenic[id];
			if (data) {
				const scenicData = { ...data };
				scenicData.viewImage = SCENIC_IMAGES[scenicData.folder].view;
				scenicData.historicalImage = SCENIC_IMAGES[scenicData.folder].index;
				this.scenic = scenicData;
				this.checkFootprintStatus();
			}
		},
		initAnimations() {
			setTimeout(() => {
				this.activeSection = 4;
				this.activeTimelineItem = 3;
				this.activeCultureCard = 3;
			}, 500);
		},
		onScroll(e) {
			this.scrollY = e.detail.scrollTop;
			this.showNavbar = this.scrollY > 200;
		},
		onTouchStart(e) { this.touchStartY = e.touches[0].clientY },
		onCompareSlide(e) {
			const touch = e.touches[0];
			const percentage = (touch.clientX / (uni.getSystemInfoSync().windowWidth)) * 100;
			this.compareSliderPosition = Math.max(0, Math.min(100, percentage));
		},
		showCultureDetail(culture) {
			this.selectedCulture = culture;
			this.showCultureModal = true;
		},
		closeCultureModal() { this.showCultureModal = false },
		startVRExperience() { uni.navigateTo({ url: `/pages/vr/experience?scenicId=${this.scenic.id}&name=${encodeURIComponent(this.scenic.name)}` }); },
		showOnMap() { uni.navigateTo({ url: `/pages/map/map?scenicId=${this.scenic.id}` }); },
		shareScenic() { uni.showToast({ title: '分享功能开发中', icon: 'none' }); },
		checkIn() {
			const key = `scenic-${this.scenic.id}`;
			const checkIns = uni.getStorageSync('checkIns') || {};

			if (this.hasCheckedIn) {
				uni.showModal({
					title: '取消打卡',
					content: '确定要抹除这个足迹吗？',
					success: (res) => {
						if (res.confirm) {
							delete checkIns[key];
							uni.setStorageSync('checkIns', checkIns);
							this.hasCheckedIn = false;
							uni.showToast({ title: '足迹已抹除', icon: 'none' });
						}
					}
				});
			} else {
				checkIns[key] = { id: this.scenic.id, type: 'scenic', name: this.scenic.name, checkedInAt: new Date().toISOString() };
				uni.setStorageSync('checkIns', checkIns);
				this.hasCheckedIn = true;
				uni.showToast({ title: '盖章成功！', icon: 'success' });
			}
		},
		checkFootprintStatus() {
			const checkIns = uni.getStorageSync('checkIns') || {};
			const key = `scenic-${this.scenic.id}`;
			this.hasCheckedIn = !!checkIns[key];
		},
		goBack() { uni.navigateBack(); }
	}
}
</script>

<style lang="scss">
.back-button {
	position: absolute;
	top: calc(var(--status-bar-height) + 20rpx);
	left: 30rpx;
	z-index: 1000;
	width: 70rpx;
	height: 70rpx;
	background-color: rgba(0, 0, 0, 0.4);
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	backdrop-filter: blur(5px);
	border: 1rpx solid rgba(255, 255, 255, 0.2);

	.back-icon {
		font-size: 50rpx;
		color: #fff;
	}
}

// 全局样式重置
.detail-container {
	width: 100vw;
	min-height: 100vh;
	background: linear-gradient(180deg, #2d1b1b 0%, #4a3226 50%, #3d2817 100%);
	position: relative;
	overflow: hidden;
}

// 沉浸式头部
.immersive-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	height: 100vh;
	z-index: 10;
	
	.hero-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	.hero-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		
		.hero-gradient {
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 60%;
			background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
		}
		
		.hero-content {
			position: absolute;
			bottom: 150rpx;
			left: 60rpx;
			right: 60rpx;
			
			.hero-title {
				display: block;
				font-size: 80rpx;
				color: #FFD700;
				font-weight: 900;
				text-shadow: 2rpx 2rpx 8rpx rgba(0, 0, 0, 0.8);
				margin-bottom: 20rpx;
				letter-spacing: 4rpx;
			}
			
			.hero-subtitle {
				display: block;
				font-size: 32rpx;
				color: #FFF8DC;
				text-shadow: 1rpx 1rpx 4rpx rgba(0, 0, 0, 0.6);
				letter-spacing: 2rpx;
			}
		}
	}
}

// 浮动导航栏
.floating-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	background: rgba(45, 27, 27, 0.9);
	backdrop-filter: blur(20rpx);
	border-bottom: 1rpx solid rgba(255, 215, 0, 0.3);
	transform: translateY(-100%);
	transition: transform 0.3s ease;
	z-index: 1000;
	
	&.navbar-visible {
		transform: translateY(0);
	}
	
	.navbar-content {
		display: flex;
		align-items: center;
		justify-content: space-around;
		padding: 20rpx 30rpx;
		padding-top: calc(20rpx + var(--status-bar-height, 0));
		
		.back-btn, .share-btn {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: rgba(255, 215, 0, 0.1);
			border-radius: 50%;
			border: 1rpx solid rgba(255, 215, 0, 0.3);
			
			.back-icon, .share-icon {
				font-size: 32rpx;
				color: #FFD700;
				font-weight: bold;
			}
		}
		
		.navbar-title {
			font-size: 32rpx;
			color: #FFD700;
			font-weight: bold;
			max-width: 400rpx;
			text-align: center;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
}

// 滚动进度指示器
.scroll-progress {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	height: 6rpx;
	background: rgba(255, 215, 0, 0.2);
	z-index: 1001;
	
	.progress-bar {
		height: 100%;
		background: linear-gradient(90deg, #FFD700, #FFA500);
		transition: width 0.1s ease;
		box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.5);
	}
}

// 滚动内容区域
.scroll-content {
	position: relative;
	z-index: 20;
	background: transparent;
}

.content-spacer {
	height: 100vh;
}

.scroll-container {
	background: linear-gradient(180deg, transparent 0%, rgba(45, 27, 27, 0.95) 10%, rgba(61, 40, 23, 0.98) 20%);
	min-height: 100vh;
	padding: 60rpx 0;
}

// 卷轴章节样式
.scroll-section {
	margin-bottom: 120rpx;
	padding: 0 60rpx;
	opacity: 0;
	transform: translateY(100rpx);
	transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
	
	&.section-active {
		opacity: 1;
		transform: translateY(0);
	}
	
	.section-header {
		display: flex;
		align-items: center;
		margin-bottom: 60rpx;
		
		.section-number {
			width: 80rpx;
			height: 80rpx;
			background: linear-gradient(135deg, #FFD700, #FFA500);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 36rpx;
			font-weight: bold;
			color: #1a1a1a;
			margin-right: 30rpx;
			box-shadow: 0 8rpx 20rpx rgba(255, 215, 0, 0.3);
		}
		
		.section-title {
			font-size: 48rpx;
			color: #FFD700;
			font-weight: bold;
			letter-spacing: 4rpx;
		}
	}
}

// 介绍卡片样式
.intro-card {
	position: relative;
	background: rgba(74, 50, 38, 0.85);
	border-radius: 20rpx;
	padding: 60rpx;
	border: 2rpx solid rgba(255, 215, 0, 0.3);
	box-shadow: 0 20rpx 40rpx rgba(45, 27, 27, 0.4);
	
	.card-ornament {
		position: absolute;
		width: 40rpx;
		height: 40rpx;
		border: 4rpx solid #FFD700;
		
		&.top-left {
			top: 20rpx;
			left: 20rpx;
			border-right: none;
			border-bottom: none;
		}
		
		&.top-right {
			top: 20rpx;
			right: 20rpx;
			border-left: none;
			border-bottom: none;
		}
		
		&.bottom-left {
			bottom: 20rpx;
			left: 20rpx;
			border-right: none;
			border-top: none;
		}
		
		&.bottom-right {
			bottom: 20rpx;
			right: 20rpx;
			border-left: none;
			border-top: none;
		}
	}
	
	.intro-content {
		.intro-text {
			font-size: 32rpx;
			line-height: 1.8;
			color: #FFF8DC;
			margin-bottom: 40rpx;
			text-indent: 2em;
		}
		
		.intro-tags {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;
			
			.tag {
				padding: 12rpx 24rpx;
				background: rgba(255, 215, 0, 0.1);
				border: 1rpx solid rgba(255, 215, 0, 0.3);
				border-radius: 30rpx;
				font-size: 24rpx;
				color: #FFD700;
			}
		}
	}
}

// 时间轴样式
.timeline-container {
	position: relative;
	
	.timeline-line {
		position: absolute;
		left: 40rpx;
		top: 0;
		bottom: 0;
		width: 4rpx;
		background: linear-gradient(180deg, #FFD700, #FFA500);
		border-radius: 2rpx;
	}
	
	.timeline-item {
		position: relative;
		padding-left: 120rpx;
		margin-bottom: 60rpx;
		opacity: 0;
		transform: translateX(-50rpx);
		transition: all 0.6s ease;
		transition-delay: var(--delay);
		
		&.timeline-active {
			opacity: 1;
			transform: translateX(0);
		}
		
		.timeline-dot {
			position: absolute;
			left: 28rpx;
			top: 10rpx;
			width: 24rpx;
			height: 24rpx;
			background: #FFD700;
			border-radius: 50%;
			box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.5);
		}
		
		.timeline-content {
			background: rgba(44, 24, 16, 0.6);
			padding: 30rpx;
			border-radius: 15rpx;
			border-left: 4rpx solid #FFD700;
			
			.timeline-period {
				display: block;
				font-size: 28rpx;
				color: #FFD700;
				font-weight: bold;
				margin-bottom: 10rpx;
			}
			
			.timeline-desc {
				font-size: 26rpx;
				color: #FFF8DC;
				line-height: 1.6;
			}
		}
	}
}

// 文化网格样式
.culture-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 30rpx;
	
	.culture-card {
		background: rgba(44, 24, 16, 0.6);
		border-radius: 20rpx;
		padding: 40rpx;
		text-align: center;
		border: 2rpx solid transparent;
		transition: all 0.4s ease;
		transform: scale(0.95);
		opacity: 0;
		
		&.card-active {
			opacity: 1;
			transform: scale(1);
		}
		
		&:active {
			transform: scale(0.98);
			border-color: #FFD700;
			background: rgba(44, 24, 16, 0.8);
		}
		
		.culture-icon {
			font-size: 60rpx;
			margin-bottom: 20rpx;
		}
		
		.culture-name {
			display: block;
			font-size: 28rpx;
			color: #FFD700;
			font-weight: bold;
			margin-bottom: 15rpx;
		}
		
		.culture-desc {
			font-size: 24rpx;
			color: #FFF8DC;
			line-height: 1.5;
		}
	}
}

// 对比区域样式
.compare-container {
	position: relative;
	height: 400rpx;
	border-radius: 20rpx;
	overflow: hidden;
	
	.compare-slider {
		position: absolute;
		top: 0;
		bottom: 0;
		width: 4rpx;
		background: #FFD700;
		z-index: 10;
		transition: left 0.1s ease;
		
		.slider-handle {
			position: absolute;
			top: 50%;
			left: -15rpx;
			width: 34rpx;
			height: 34rpx;
			background: #FFD700;
			border-radius: 50%;
			transform: translateY(-50%);
			box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.4);
		}
		
		.slider-label {
			position: absolute;
			top: 20rpx;
			left: -60rpx;
			width: 120rpx;
			text-align: center;
			font-size: 24rpx;
			color: #FFD700;
			background: rgba(26, 26, 26, 0.8);
			padding: 8rpx;
			border-radius: 10rpx;
		}
	}
	
	.compare-images {
		position: relative;
		width: 100%;
		height: 100%;
		
		.compare-image {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			
			&.modern {
				z-index: 5;
			}
		}
	}
}

// 指南样式
.guide-content {
	.guide-item {
		display: flex;
		align-items: center;
		background: rgba(44, 24, 16, 0.6);
		border-radius: 15rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		border-left: 4rpx solid #FFD700;
		
		.guide-icon {
			font-size: 40rpx;
			margin-right: 30rpx;
		}
		
		.guide-text {
			flex: 1;
			
			.guide-label {
				display: block;
				font-size: 24rpx;
				color: #FFD700;
				margin-bottom: 8rpx;
			}
			
			.guide-value {
				font-size: 28rpx;
				color: #FFF8DC;
				line-height: 1.5;
			}
		}
	}
}

// 底部操作区
.bottom-actions {
	margin-top: 40rpx;
	
	.action-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 30rpx;
		padding: 0 60rpx;
		
		.action-item {
			background: rgba(44, 24, 16, 0.8);
			border-radius: 20rpx;
			padding: 40rpx;
			text-align: center;
			border: 2rpx solid rgba(255, 215, 0, 0.2);
			transition: all 0.3s ease;
			
			&.map-back-item {
				background: linear-gradient(135deg, #FFD700, #FFA500);
				border: 3rpx solid #8B4513;
				transform: scale(1.05);
				
				.action-icon {
					font-size: 60rpx;
				}
				
				.action-text {
					color: #1a1a1a;
					font-size: 30rpx;
				}
				
				&:active {
					transform: scale(1.0);
					background: linear-gradient(135deg, #FFA500, #FF8C00);
				}
			}
			
			&:active {
				transform: scale(0.95);
				background: rgba(44, 24, 16, 0.9);
				border-color: #FFD700;
			}
			
			.action-icon {
				font-size: 50rpx;
				margin-bottom: 15rpx;
			}
			
			.action-text {
				display: block;
				font-size: 26rpx;
				color: #FFF8DC;
				font-weight: bold;
			}
		}
	}
}

// 文化详情弹窗
.culture-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
	padding: 60rpx;
	
	.modal-content {
		background: #1a1a1a;
		border-radius: 20rpx;
		border: 2rpx solid #FFD700;
		max-width: 600rpx;
		max-height: 80vh;
		overflow: hidden;
		
		.modal-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx 40rpx;
			border-bottom: 1rpx solid rgba(255, 215, 0, 0.2);
			
			.modal-title {
				font-size: 32rpx;
				color: #FFD700;
				font-weight: bold;
			}
			
			.modal-close {
				width: 50rpx;
				height: 50rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 36rpx;
				color: #FFD700;
				background: rgba(255, 215, 0, 0.1);
				border-radius: 50%;
			}
		}
		
		.modal-body {
			padding: 40rpx;
			
			.modal-text {
				font-size: 28rpx;
				color: #FFF8DC;
				line-height: 1.8;
			}
		}
	}
}

.checkin-icon {
	background-color: #f0f0f0;
	color: #8B4513;
	font-weight: bold;
	border: 2rpx solid #8B4513;

	&.checked-in {
		background-color: #8B4513;
		color: #FFD700;
		transform: scale(1.1) rotate(15deg);
		box-shadow: 0 0 15rpx #FFD700;
	}
}
</style> 